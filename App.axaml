<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="LSSOFT.App"
             xmlns:local="using:LSSOFT"
             xmlns:converters="using:LSSOFT.Converters"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <Application.Resources>
        <!-- 全局字体图标 -->
        <FontFamily x:Key="IconFont">avares://LSSOFT/Assets/font/iconfont.ttf#iconfont</FontFamily>

        <!-- 转换器 -->
        <converters:CategoryToColorConverter x:Key="CategoryToColorConverter" />
    </Application.Resources>

    <Application.Styles>
        <FluentTheme />
    </Application.Styles>
</Application>