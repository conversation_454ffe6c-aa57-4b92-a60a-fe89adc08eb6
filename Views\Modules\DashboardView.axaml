<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="LSSOFT.Views.Modules.DashboardView"
             Background="#F8F9FA">

    <ScrollViewer>
        <StackPanel Margin="20">
            <!-- 欢迎标题 -->
            <TextBlock Text="欢迎使用个人软件综合管理系统" 
                      FontSize="28" FontWeight="Bold" 
                      Foreground="#2D3748" Margin="0,0,0,20"/>

            <!-- 系统概览卡片 -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 已安装软件数量 -->
                <Border Grid.Column="0" Background="#BEE3F8" CornerRadius="8" Padding="20" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="📦" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="已安装软件" FontSize="14" HorizontalAlignment="Center" Foreground="#2D3748"/>
                        <TextBlock Text="156" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1A365D"/>
                    </StackPanel>
                </Border>

                <!-- 快捷方式数量 -->
                <Border Grid.Column="1" Background="#C6F6D5" CornerRadius="8" Padding="20" Margin="5,0">
                    <StackPanel>
                        <TextBlock Text="🔗" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="快捷方式" FontSize="14" HorizontalAlignment="Center" Foreground="#2D3748"/>
                        <TextBlock Text="89" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1A202C"/>
                    </StackPanel>
                </Border>

                <!-- 系统优化项 -->
                <Border Grid.Column="2" Background="#FBD38D" CornerRadius="8" Padding="20" Margin="5,0">
                    <StackPanel>
                        <TextBlock Text="🧹" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="优化项目" FontSize="14" HorizontalAlignment="Center" Foreground="#2D3748"/>
                        <TextBlock Text="23" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#744210"/>
                    </StackPanel>
                </Border>

                <!-- 自动化任务 -->
                <Border Grid.Column="3" Background="#FEB2B2" CornerRadius="8" Padding="20" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="⚡" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="自动任务" FontSize="14" HorizontalAlignment="Center" Foreground="#2D3748"/>
                        <TextBlock Text="12" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#742A2A"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 快速操作 -->
            <TextBlock Text="快速操作" FontSize="20" FontWeight="Bold" Foreground="#2D3748" Margin="0,0,0,15"/>
            
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0" Classes="quick-action" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="🔍" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="扫描已安装软件" FontSize="14" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Grid.Column="1" Classes="quick-action" Margin="5,0">
                    <StackPanel>
                        <TextBlock Text="🧹" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="系统清理" FontSize="14" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Grid.Column="2" Classes="quick-action" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="⚙️" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="系统设置" FontSize="14" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>

            <!-- 最近活动 -->
            <TextBlock Text="最近活动" FontSize="20" FontWeight="Bold" Foreground="#2D3748" Margin="0,0,0,15"/>
            
            <Border Background="#F7FAFC" CornerRadius="8" Padding="20">
                <StackPanel>
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="📦" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Grid.Column="1" Text="安装了 Visual Studio Code" FontSize="14" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="2小时前" FontSize="12" Foreground="#718096" VerticalAlignment="Center"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="🧹" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Grid.Column="1" Text="清理了临时文件 (1.2GB)" FontSize="14" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="昨天" FontSize="12" Foreground="#718096" VerticalAlignment="Center"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="🔗" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Grid.Column="1" Text="整理了桌面快捷方式" FontSize="14" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="3天前" FontSize="12" Foreground="#718096" VerticalAlignment="Center"/>
                    </Grid>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="⚡" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Grid.Column="1" Text="创建了自动化脚本" FontSize="14" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="1周前" FontSize="12" Foreground="#718096" VerticalAlignment="Center"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 系统信息 -->
            <TextBlock Text="系统信息" FontSize="20" FontWeight="Bold" Foreground="#2D3748" Margin="0,30,0,15"/>
            
            <Border Background="#F7FAFC" CornerRadius="8" Padding="20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="操作系统: Windows 11 Pro" FontSize="14" Margin="0,0,0,5"/>
                        <TextBlock Text="处理器: Intel Core i7-12700K" FontSize="14" Margin="0,0,0,5"/>
                        <TextBlock Text="内存: 32GB DDR4" FontSize="14" Margin="0,0,0,5"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1">
                        <TextBlock Text="磁盘空间: 512GB SSD" FontSize="14" Margin="0,0,0,5"/>
                        <TextBlock Text="可用空间: 256GB" FontSize="14" Margin="0,0,0,5"/>
                        <TextBlock Text="系统运行时间: 2天 14小时" FontSize="14" Margin="0,0,0,5"/>
                    </StackPanel>
                </Grid>
            </Border>
        </StackPanel>
    </ScrollViewer>

    <UserControl.Styles>
        <Style Selector="Button.quick-action">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="MinHeight" Value="80"/>
        </Style>
        <Style Selector="Button.quick-action:pointerover">
            <Setter Property="Background" Value="#F7FAFC"/>
            <Setter Property="BorderBrush" Value="#CBD5E0"/>
        </Style>
    </UserControl.Styles>

</UserControl>
