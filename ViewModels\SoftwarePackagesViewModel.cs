using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Models;
using Avalonia;

namespace LSSOFT.ViewModels;

/// <summary>
/// 软件安装包管理视图模型
/// </summary>
public partial class SoftwarePackagesViewModel : ViewModelBase
{
    [ObservableProperty]
    private ObservableCollection<SoftwarePackageInfo> _softwarePackages = new();

    [ObservableProperty]
    private ObservableCollection<SoftwarePackageInfo> _filteredSoftwarePackages = new();

    [ObservableProperty]
    private ObservableCollection<SoftwareCategoryInfo> _categories = new();



    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private string _selectedCategory = "全部";

    [ObservableProperty]
    private bool _isAddCategoryDialogVisible;

    [ObservableProperty]
    private string _newCategoryName = string.Empty;

    [ObservableProperty]
    private bool _isCardView = true;

    [ObservableProperty]
    private bool _isListView = false;



    public SoftwarePackagesViewModel()
    {
        InitializeCategories();
        InitializeSampleData();
        ApplyFilters();

        // 监听搜索文本变化
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(SearchText))
            {
                ApplyFilters();
            }
        };
    }

    /// <summary>
    /// 初始化分类数据
    /// </summary>
    private void InitializeCategories()
    {
        Categories.Clear();
        string dbPath = Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "lssoft.js");
        if (File.Exists(dbPath))
        {
            try
            {
                string json = File.ReadAllText(dbPath, System.Text.Encoding.UTF8);
                int brace = json.IndexOf('{');
                if (brace > 0) json = json.Substring(brace); // 跳过注释
                using var doc = JsonDocument.Parse(json);
                var root = doc.RootElement;
                if (root.TryGetProperty("categories", out var arr))
                {
                    foreach (var item in arr.EnumerateArray())
                    {
                        string name = item.GetProperty("name").GetString() ?? "";
                        int count = item.GetProperty("count").GetInt32();
                        Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = name, Count = count });
                    }
                }
            }
            catch { }
        }
        if (Categories.Count == 0)
        {
            // 兜底默认
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "全部", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "系统", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "办公", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "开发", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "设计", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "游戏", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "手机", Count = 0 });
            Categories.Add(new LSSOFT.Models.SoftwareCategoryInfo { Name = "其它", Count = 0 });
        }
        // 默认选中第一个
        if (Categories.Count > 0)
            Categories[0].IsSelected = true;
    }

    /// <summary>
    /// 初始化示例数据
    /// </summary>
    private void InitializeSampleData()
    {
        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "VS Code",
            Version = "1.098.1",
            LatestVersion = "1.100.1",
            Description = "Visual Studio Code 是一款可在 Windows、macOS 和 Linux 上运行的独立源代码编辑器，支持几乎任何编程语言。",
            Size = "105Mb",
            Type = SoftwareType.Development,
            License = "MIT",
            Stars = "162k",
            Downloads = "50M+",
            SupportDonation = false,
            SupportAI = true,
            Content = "这是VS Code的详细内容 - 强大的代码编辑器，支持多种编程语言和丰富的扩展生态系统。",
            HasUpdate = true,
            CanCrack = true,
            IsOpenSource = true
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "Adobe Photoshop",
            Version = "2024.1",
            LatestVersion = "2024.2",
            Description = "专业的图像编辑和设计软件，广泛用于照片编辑、数字艺术创作和网页设计。",
            Size = "2.1GB",
            Type = SoftwareType.Multimedia,
            License = "Commercial",
            Stars = "4.5★",
            Downloads = "100M+",
            SupportDonation = false,
            SupportAI = false,
            Content = "这是Adobe Photoshop的详细内容 - 业界标准的图像编辑软件。",
            HasUpdate = true,
            CanCrack = true,
            IsOpenSource = false
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "Microsoft Office",
            Version = "2021",
            LatestVersion = "2024",
            Description = "包含Word、Excel、PowerPoint等办公应用的完整办公套件。",
            Size = "3.5GB",
            Type = SoftwareType.Office,
            License = "Commercial",
            Stars = "4.7★",
            Downloads = "1B+",
            SupportDonation = false,
            SupportAI = false,
            Content = "这是Microsoft Office的详细内容 - 完整的办公解决方案。",
            HasUpdate = false,
            CanCrack = true,
            IsOpenSource = false
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "AutoCAD",
            Version = "2024.2",
            LatestVersion = "2025.1",
            Description = "专业的计算机辅助设计(CAD)软件，用于2D和3D设计与制图。",
            Size = "1.8GB",
            Type = SoftwareType.Engineering,
            License = "Commercial",
            Stars = "4.2★",
            Downloads = "10M+",
            SupportDonation = false,
            SupportAI = false,
            Content = "这是AutoCAD的详细内容 - 专业的CAD设计软件。",
            HasUpdate = true,
            CanCrack = false,
            IsOpenSource = false
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "Steam",
            Version = "3.5.2",
            LatestVersion = "3.6.0",
            Description = "数字游戏发行平台，提供游戏购买、下载、更新和社区功能。",
            Size = "256MB",
            Type = SoftwareType.Game,
            License = "Freeware",
            Stars = "4.8★",
            Downloads = "500M+",
            SupportDonation = false,
            SupportAI = false,
            Content = "这是Steam的详细内容 - 全球最大的PC游戏平台。"
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "Google Chrome",
            Version = "120.0.1",
            LatestVersion = "121.0.2",
            Description = "快速、安全的网页浏览器，支持现代网页标准和丰富的扩展。",
            Size = "180MB",
            Type = SoftwareType.Network,
            License = "Freeware",
            Stars = "4.6★",
            Downloads = "5B+",
            SupportDonation = false,
            SupportAI = false,
            Content = "这是Google Chrome的详细内容 - 世界上最受欢迎的网页浏览器。"
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "WinRAR",
            Version = "6.24",
            LatestVersion = "7.01",
            Description = "强大的文件压缩和解压缩工具，支持多种压缩格式。",
            Size = "3.2MB",
            Type = SoftwareType.Other,
            License = "Shareware",
            Stars = "4.1★",
            Downloads = "200M+",
            SupportDonation = true,
            SupportAI = false,
            Content = "这是WinRAR的详细内容 - 经典的文件压缩工具。"
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "VLC Media Player",
            Version = "3.0.18",
            LatestVersion = "3.0.20",
            Description = "免费的多媒体播放器，支持几乎所有音频和视频格式。",
            Size = "42MB",
            Type = SoftwareType.Multimedia,
            License = "GPL-2.0",
            Stars = "13.8k",
            Downloads = "3B+",
            SupportDonation = true,
            SupportAI = false,
            Content = "这是VLC Media Player的详细内容 - 万能的媒体播放器。"
        });

        SoftwarePackages.Add(new SoftwarePackageInfo
        {
            Name = "Notepad++",
            Version = "8.5.8",
            LatestVersion = "8.6.2",
            Description = "免费的源代码编辑器，支持多种编程语言的语法高亮。",
            Size = "4.1MB",
            Type = SoftwareType.Development,
            License = "GPL-3.0",
            Stars = "22.4k",
            Downloads = "100M+",
            SupportDonation = true,
            SupportAI = true,
            Content = "这是Notepad++的详细内容 - 轻量级的代码编辑器。"
        });
    }



    /// <summary>
    /// 按分类筛选命令
    /// </summary>
    [RelayCommand]
    private void FilterByCategory(string categoryName)
    {
        // 更新分类选中状态
        foreach (var category in Categories)
        {
            category.IsSelected = category.Name == categoryName;
        }

        SelectedCategory = categoryName;
        ApplyFilters();
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private void Refresh()
    {
        // 重新加载数据
        InitializeSampleData();
        ApplyFilters();
    }

    /// <summary>
    /// 显示添加分类对话框命令
    /// </summary>
    [RelayCommand]
    private void ShowAddCategoryDialog()
    {
        NewCategoryName = string.Empty;
        IsAddCategoryDialogVisible = true;
    }

    /// <summary>
    /// 添加分类命令
    /// </summary>
    [RelayCommand]
    private void AddCategory()
    {
        if (string.IsNullOrWhiteSpace(NewCategoryName))
            return;

        // 检查是否已存在
        if (Categories.Any(c => c.Name == NewCategoryName))
            return;

        // 添加新分类
        Categories.Add(new SoftwareCategoryInfo(NewCategoryName, isCustom: true));

        IsAddCategoryDialogVisible = false;
        NewCategoryName = string.Empty;
    }

    /// <summary>
    /// 取消添加分类命令
    /// </summary>
    [RelayCommand]
    private void CancelAddCategory()
    {
        IsAddCategoryDialogVisible = false;
        NewCategoryName = string.Empty;
    }

    /// <summary>
    /// 切换到卡片视图命令
    /// </summary>
    [RelayCommand]
    private void SwitchToCardView()
    {
        IsCardView = true;
        IsListView = false;
    }

    /// <summary>
    /// 切换到列表视图命令
    /// </summary>
    [RelayCommand]
    private void SwitchToListView()
    {
        IsCardView = false;
        IsListView = true;
    }

    /// <summary>
    /// 导出数据命令
    /// </summary>
    [RelayCommand]
    private void ExportData()
    {
        // TODO: 实现导出数据功能
        // 可以导出为Excel、CSV等格式
        System.Diagnostics.Debug.WriteLine("导出数据功能待实现");
    }

    /// <summary>
    /// 删除选中项命令
    /// </summary>
    [RelayCommand]
    private void DeleteSelected()
    {
        // TODO: 实现删除选中项功能
        // 需要先实现多选功能，然后删除选中的软件包
        System.Diagnostics.Debug.WriteLine("删除选中项功能待实现");
    }

    /// <summary>
    /// 显示更多操作命令
    /// </summary>
    [RelayCommand]
    private void ShowMoreActions()
    {
        // TODO: 实现更多操作菜单
        // 可以显示上下文菜单或弹出窗口，包含更多操作选项
        System.Diagnostics.Debug.WriteLine("更多操作功能待实现");
    }

    /// <summary>
    /// 应用筛选条件
    /// </summary>
    private void ApplyFilters()
    {
        var filtered = SoftwarePackages.AsEnumerable();

        // 按分类筛选
        if (SelectedCategory != "全部")
        {
            var selectedCategoryInfo = Categories.FirstOrDefault(c => c.Name == SelectedCategory);
            if (selectedCategoryInfo?.Type != null)
            {
                filtered = filtered.Where(p => p.Type == selectedCategoryInfo.Type);
            }
        }

        // 按搜索文本筛选
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            var searchLower = SearchText.ToLower();
            filtered = filtered.Where(p =>
                p.Name.ToLower().Contains(searchLower) ||
                p.Description.ToLower().Contains(searchLower));
        }

        // 更新筛选结果
        FilteredSoftwarePackages.Clear();
        foreach (var package in filtered)
        {
            FilteredSoftwarePackages.Add(package);
        }
    }

    /// <summary>
    /// 忽略版本更新命令
    /// </summary>
    [RelayCommand]
    private void IgnoreUpdate(SoftwarePackageInfo package)
    {
        // 这里可以实现忽略版本更新的逻辑
        System.Diagnostics.Debug.WriteLine($"忽略 {package?.Name} 的版本更新");
    }

    /// <summary>
    /// 不再更新命令
    /// </summary>
    [RelayCommand]
    private void StopUpdate(SoftwarePackageInfo package)
    {
        // 这里可以实现不再更新的逻辑
        System.Diagnostics.Debug.WriteLine($"不再更新 {package?.Name}");
    }

    /// <summary>
    /// 打开安装目录命令
    /// </summary>
    [RelayCommand]
    private void OpenInstallDir(SoftwarePackageInfo package)
    {
        // 这里可以实现打开安装目录的逻辑
        System.Diagnostics.Debug.WriteLine($"打开 {package?.Name} 的安装目录");
    }
}
