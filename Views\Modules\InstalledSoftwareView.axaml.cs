using Avalonia.Controls;
using LSSOFT.ViewModels;
using LSSOFT.Services;
using LSSOFT.Models;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;

namespace LSSOFT.Views.Modules;

// 软件显示项类
public class SoftwareDisplayItem
{
    public string 软件名称 { get; set; } = "";
    public string 版本 { get; set; } = "";
    public string 发布商 { get; set; } = "";
    public string 大小 { get; set; } = "";
    public string 安装日期 { get; set; } = "";
    public string 安装路径 { get; set; } = "";
    public string 图标路径 { get; set; } = "";
    public bool IsSelected { get; set; } = false;  // 添加选中状态属性
}

public partial class InstalledSoftwareView : UserControl
{
    private readonly WindowsSoftwareScanner _scanner;
    private List<SoftwareDisplayItem> _allSoftwareItems = new List<SoftwareDisplayItem>();
    private System.Timers.Timer? _searchTimer;
    private string _lastSearchText = "";
    private bool _isSearching = false;
    private bool _isClearing = false;
    private SoftwareDisplayItem? _selectedItem = null;  // 当前选中的项目
    private Border? _selectedRowBorder = null;  // 当前选中的行边框
    private CheckBox? _selectedCheckBox = null;  // 当前选中的复选框

    // 分批加载相关字段
    private List<SoftwareDisplayItem> _allLoadedItems = new List<SoftwareDisplayItem>();  // 所有已加载的软件
    private bool _isFullyLoaded = false;  // 是否已完全加载
    private int _currentBatchSize = 20;  // 每批加载数量
    private int _loadedCount = 0;  // 已加载数量

    // 分类筛选相关字段
    private int _currentFilterIndex = 0;  // 当前筛选索引 (0=全部软件)

    public InstalledSoftwareView()
    {
        InitializeComponent();
        _scanner = new WindowsSoftwareScanner();

        // 初始化搜索防抖动计时器
        InitializeSearchTimer();

        // 在控件加载完成后直接加载数据
        Loaded += OnLoaded;
    }

    private void InitializeSearchTimer()
    {
        _searchTimer = new System.Timers.Timer(300); // 300ms 防抖动延迟
        _searchTimer.Elapsed += async (sender, e) =>
        {
            _searchTimer.Stop();

            // 在UI线程上执行搜索
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                PerformSearchInternal(_lastSearchText);
            });
        };
        _searchTimer.AutoReset = false;
    }

    private async void OnLoaded(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("InstalledSoftwareView 已加载，开始初始化...");

            // 初始化ComboBox事件处理
            InitializeComboBoxEvents();

            // 开始分批加载软件数据
            await LoadSoftwareDataBatchAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"OnLoaded 失败: {ex.Message}");
        }
    }

    private async Task LoadSoftwareDataAsync()
    {
        try
        {
            // 更新状态栏显示正在扫描
            UpdateStatusBar("正在扫描...", "计算中...");

            System.Diagnostics.Debug.WriteLine("开始扫描已安装的软件...");

            // 扫描真实的已安装软件
            var installedSoftware = await _scanner.ScanInstalledSoftwareAsync();

            // 转换为显示项
            var displayItems = installedSoftware.Select(software => new SoftwareDisplayItem
            {
                软件名称 = software.Name,
                版本 = software.Version,
                发布商 = software.Publisher,
                大小 = _scanner.FormatFileSize(software.EstimatedSize),
                安装日期 = software.InstallDate,
                安装路径 = string.IsNullOrEmpty(software.InstallLocation) ? "未知" : software.InstallLocation,
                图标路径 = software.DisplayIcon
            }).ToList();

            // 按安装时间排序，最新安装的软件排在最前面
            _allSoftwareItems = SortSoftwareByInstallDate(displayItems);

            System.Diagnostics.Debug.WriteLine($"扫描完成，找到 {_allSoftwareItems.Count} 个软件");

            // 显示所有软件（初始状态）
            DisplaySoftwareList(_allSoftwareItems);

            // 更新状态栏信息
            UpdateStatusBar(_allSoftwareItems.Count, installedSoftware.Sum(s => s.EstimatedSize));
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载数据失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
        }
    }

    // 初始化ComboBox事件处理
    private void InitializeComboBoxEvents()
    {
        try
        {
            // 查找分类筛选ComboBox
            if (this.FindControl<ComboBox>("CategoryComboBox") is ComboBox categoryComboBox)
            {
                categoryComboBox.SelectionChanged += OnCategorySelectionChanged;
                System.Diagnostics.Debug.WriteLine("分类筛选ComboBox事件已绑定");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("未找到分类筛选ComboBox");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"初始化ComboBox事件失败: {ex.Message}");
        }
    }

    // 分批加载软件数据
    private async Task LoadSoftwareDataBatchAsync()
    {
        try
        {
            // 更新状态栏显示正在扫描
            UpdateStatusBar("正在扫描软件...", "计算中...");

            System.Diagnostics.Debug.WriteLine("开始分批加载软件数据...");

            // 扫描所有已安装软件
            var installedSoftware = await _scanner.ScanInstalledSoftwareAsync();

            // 转换为显示项并添加分类信息
            var allDisplayItems = installedSoftware.Select(software => new SoftwareDisplayItem
            {
                软件名称 = software.Name,
                版本 = software.Version,
                发布商 = software.Publisher,
                大小 = _scanner.FormatFileSize(software.EstimatedSize),
                安装日期 = software.InstallDate,
                安装路径 = string.IsNullOrEmpty(software.InstallLocation) ? "未知" : software.InstallLocation,
                图标路径 = software.DisplayIcon
            }).ToList();

            // 按安装时间排序
            _allLoadedItems = SortSoftwareByInstallDate(allDisplayItems);
            _allSoftwareItems = _allLoadedItems; // 保持兼容性

            System.Diagnostics.Debug.WriteLine($"扫描完成，找到 {_allLoadedItems.Count} 个软件");

            // 先显示前20条记录
            var initialBatch = _allLoadedItems.Take(_currentBatchSize).ToList();
            _loadedCount = initialBatch.Count;

            DisplaySoftwareList(initialBatch);

            // 更新状态栏
            UpdateStatusBar($"已显示: {_loadedCount} 个软件", $"总计: {_allLoadedItems.Count} 个");

            // 如果还有更多软件，继续加载
            if (_allLoadedItems.Count > _currentBatchSize)
            {
                _ = Task.Run(async () =>
                {
                    await Task.Delay(500); // 短暂延迟，让用户看到初始结果
                    await LoadRemainingItemsAsync();
                });
            }
            else
            {
                _isFullyLoaded = true;
                UpdateStatusBar($"已安装软件: {_allLoadedItems.Count} 个", $"总大小: {CalculateTotalSize()}");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"分批加载数据失败: {ex.Message}");
            UpdateStatusBar("加载失败", "请重试");
        }
    }

    // 加载剩余项目
    private async Task LoadRemainingItemsAsync()
    {
        try
        {
            while (_loadedCount < _allLoadedItems.Count)
            {
                var nextBatch = _allLoadedItems.Skip(_loadedCount).Take(_currentBatchSize).ToList();
                if (!nextBatch.Any()) break;

                // 在UI线程上添加新项目
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    AddItemsToDisplay(nextBatch);
                    _loadedCount += nextBatch.Count;

                    // 更新状态栏
                    if (_loadedCount >= _allLoadedItems.Count)
                    {
                        _isFullyLoaded = true;
                        UpdateStatusBar($"已安装软件: {_allLoadedItems.Count} 个", $"总大小: {CalculateTotalSize()}");
                    }
                    else
                    {
                        UpdateStatusBar($"已显示: {_loadedCount} 个软件", $"总计: {_allLoadedItems.Count} 个");
                    }
                });

                // 短暂延迟，避免UI阻塞
                await Task.Delay(100);
            }

            System.Diagnostics.Debug.WriteLine($"所有软件加载完成，共 {_allLoadedItems.Count} 个");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载剩余项目失败: {ex.Message}");
        }
    }

    // 显示软件列表的方法（优化版本）
    private void DisplaySoftwareList(List<SoftwareDisplayItem> displayItems)
    {
        try
        {
            // 动态创建软件列表
            if (SoftwareListPanel != null)
            {
                // 清除现有内容和选中状态引用
                SoftwareListPanel.Children.Clear();
                _selectedItem = null;
                _selectedRowBorder = null;
                _selectedCheckBox = null;

                // 如果列表为空，显示提示信息
                if (!displayItems.Any())
                {
                    var emptyMessage = new TextBlock
                    {
                        Text = "未找到匹配的软件",
                        FontSize = 14,
                        Foreground = Avalonia.Media.Brush.Parse("#999"),
                        HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Margin = new Avalonia.Thickness(0, 50)
                    };
                    SoftwareListPanel.Children.Add(emptyMessage);
                    return;
                }

                // 批量添加软件行，提高性能
                var itemsToAdd = new List<Border>();

                // 添加软件数据行
                foreach (var software in displayItems)
                {
                    var rowBorder = new Border
                    {
                        BorderBrush = Avalonia.Media.Brush.Parse("#F0F0F0"),
                        BorderThickness = new Avalonia.Thickness(0, 0, 0, 1),
                        Padding = new Avalonia.Thickness(15, 8),
                        Background = software.IsSelected ?
                            Avalonia.Media.Brush.Parse("#E3F2FD") :  // 选中时的蓝色背景
                            Avalonia.Media.Brush.Parse("White"),     // 默认白色背景
                        Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Hand)  // 鼠标悬停时显示手型光标
                    };

                    // 为行添加点击事件
                    rowBorder.PointerPressed += (sender, e) => OnRowClicked(software, rowBorder);

                    // 添加鼠标悬停效果
                    rowBorder.PointerEntered += (sender, e) => OnRowPointerEntered(rowBorder, software.IsSelected);
                    rowBorder.PointerExited += (sender, e) => OnRowPointerExited(rowBorder, software.IsSelected);

                    var rowGrid = new Grid();
                    // 列定义顺序：复选框、软件名称、版本、大小、安装日期、安装路径、发布商
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(40) });      // 复选框
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(300) });     // 软件名称
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });     // 版本
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });     // 大小
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });     // 安装日期
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // 安装路径
                    rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(140) });     // 发布商

                    // 复选框
                    var checkBox = new CheckBox
                    {
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        IsChecked = software.IsSelected  // 与选中状态同步
                    };

                    // 复选框点击事件
                    checkBox.Click += (sender, e) => OnCheckBoxClicked(software, rowBorder, checkBox);

                    Grid.SetColumn(checkBox, 0);

                    // 软件名称（带图标）
                    var namePanel = new StackPanel
                    {
                        Orientation = Avalonia.Layout.Orientation.Horizontal,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
                    };

                    // 尝试加载真实图标，失败则使用默认图标
                    var iconElement = CreateSoftwareIcon(software.图标路径, software.软件名称);

                    // 调试：输出图标路径信息
                    if (!string.IsNullOrEmpty(software.图标路径))
                    {
                        System.Diagnostics.Debug.WriteLine($"软件: {software.软件名称}, 图标路径: {software.图标路径}");
                    }

                    var nameText = new TextBlock
                    {
                        Text = software.软件名称,
                        FontSize = 12,
                        FontWeight = Avalonia.Media.FontWeight.Medium,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        TextTrimming = Avalonia.Media.TextTrimming.CharacterEllipsis,
                        MaxWidth = 250  // 调整为与新的列宽度匹配
                    };

                    namePanel.Children.Add(iconElement);
                    namePanel.Children.Add(nameText);
                    Grid.SetColumn(namePanel, 1); // 软件名称 - 第2列

                    // 版本
                    var versionText = new TextBlock
                    {
                        Text = software.版本,
                        FontSize = 11,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Foreground = Avalonia.Media.Brush.Parse("#6C757D")
                    };
                    Grid.SetColumn(versionText, 2); // 版本 - 第3列

                    // 大小
                    var sizeText = new TextBlock
                    {
                        Text = software.大小,
                        FontSize = 11,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Foreground = Avalonia.Media.Brush.Parse("#6C757D")
                    };
                    Grid.SetColumn(sizeText, 3); // 大小 - 第4列

                    // 安装日期
                    var dateText = new TextBlock
                    {
                        Text = software.安装日期,
                        FontSize = 11,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Foreground = Avalonia.Media.Brush.Parse("#6C757D")
                    };
                    Grid.SetColumn(dateText, 4); // 安装日期 - 第5列

                    // 安装路径
                    var pathText = new TextBlock
                    {
                        Text = software.安装路径,
                        FontSize = 11,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Foreground = Avalonia.Media.Brush.Parse("#6C757D"),
                        TextTrimming = Avalonia.Media.TextTrimming.CharacterEllipsis
                    };
                    Grid.SetColumn(pathText, 5); // 安装路径 - 第6列

                    // 发布商
                    var publisherText = new TextBlock
                    {
                        Text = software.发布商,
                        FontSize = 11,
                        VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                        Foreground = Avalonia.Media.Brush.Parse("#6C757D")
                    };
                    Grid.SetColumn(publisherText, 6); // 发布商 - 第7列

                    // 按照列顺序添加控件：复选框、软件名称、版本、大小、安装日期、安装路径、发布商
                    rowGrid.Children.Add(checkBox);        // 第1列：复选框
                    rowGrid.Children.Add(namePanel);       // 第2列：软件名称
                    rowGrid.Children.Add(versionText);     // 第3列：版本
                    rowGrid.Children.Add(sizeText);        // 第4列：大小
                    rowGrid.Children.Add(dateText);        // 第5列：安装日期
                    rowGrid.Children.Add(pathText);        // 第6列：安装路径
                    rowGrid.Children.Add(publisherText);   // 第7列：发布商

                    rowBorder.Child = rowGrid;
                    itemsToAdd.Add(rowBorder);
                }

                // 批量添加到UI，减少重绘次数
                foreach (var item in itemsToAdd)
                {
                    SoftwareListPanel.Children.Add(item);
                }

                System.Diagnostics.Debug.WriteLine($"成功创建了 {displayItems.Count} 个软件行");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("错误：SoftwareListPanel 为 null");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"显示软件列表失败: {ex.Message}");
        }
    }

    // 刷新按钮点击事件处理程序
    private async void RefreshButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("用户点击刷新按钮，开始重新扫描软件...");

            // 禁用刷新按钮防止重复点击
            if (this.FindControl<Button>("RefreshButton") is Button refreshBtn)
            {
                refreshBtn.IsEnabled = false;
                refreshBtn.Content = "⏳"; // 显示加载图标
            }

            // 重新加载数据
            await LoadSoftwareDataAsync();

            // 刷新后保持当前搜索状态
            if (this.FindControl<TextBox>("SearchTextBox") is TextBox searchBox)
            {
                var currentSearchText = searchBox.Text?.Trim() ?? "";
                if (!string.IsNullOrEmpty(currentSearchText))
                {
                    PerformSearchInternal(currentSearchText);
                }
            }

            System.Diagnostics.Debug.WriteLine("刷新完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"刷新失败: {ex.Message}");
            UpdateStatusBar("刷新失败", "请重试");
        }
        finally
        {
            // 恢复刷新按钮
            if (this.FindControl<Button>("RefreshButton") is Button refreshBtn)
            {
                refreshBtn.IsEnabled = true;
                refreshBtn.Content = "🔄"; // 恢复刷新图标
            }
        }
    }

    // 搜索文本框文本变化事件处理程序（使用防抖动）
    private void SearchTextBox_TextChanged(object? sender, Avalonia.Controls.TextChangedEventArgs e)
    {
        try
        {
            // 如果正在清除搜索，跳过此次事件处理
            if (_isClearing)
            {
                System.Diagnostics.Debug.WriteLine("正在清除搜索，跳过TextChanged事件");
                return;
            }

            if (sender is TextBox searchBox)
            {
                var searchText = searchBox.Text?.Trim() ?? "";

                // 立即显示/隐藏清除按钮（无需延迟）
                UpdateClearButtonVisibility(!string.IsNullOrEmpty(searchText));

                // 如果搜索文本没有变化，直接返回
                if (searchText == _lastSearchText)
                    return;

                _lastSearchText = searchText;

                // 使用防抖动机制延迟搜索
                _searchTimer?.Stop();

                // 如果搜索文本为空，立即显示所有结果
                if (string.IsNullOrEmpty(searchText))
                {
                    PerformSearchInternal(searchText);
                }
                else
                {
                    // 对于非空搜索，使用防抖动延迟
                    _searchTimer?.Start();
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"搜索处理失败: {ex.Message}");
        }
    }

    // 清除搜索按钮点击事件处理程序
    private void ClearSearchButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
    {
        try
        {
            if (this.FindControl<TextBox>("SearchTextBox") is TextBox searchBox)
            {
                // 设置清除标志，防止TextChanged事件触发搜索
                _isClearing = true;

                // 停止任何正在进行的搜索计时器
                _searchTimer?.Stop();

                // 清除文本
                searchBox.Text = "";
                _lastSearchText = "";

                // 立即显示所有软件，不使用防抖动
                PerformSearchInternal("");

                // 聚焦到搜索框
                searchBox.Focus();

                // 重置清除标志
                _isClearing = false;

                System.Diagnostics.Debug.WriteLine("清除搜索文本并立即显示所有结果");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清除搜索失败: {ex.Message}");
        }
    }

    // 更新清除按钮的可见性
    private void UpdateClearButtonVisibility(bool isVisible)
    {
        try
        {
            if (this.FindControl<Button>("ClearSearchButton") is Button clearButton)
            {
                clearButton.IsVisible = isVisible;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新清除按钮可见性失败: {ex.Message}");
        }
    }

    // 按安装时间排序软件列表
    private List<SoftwareDisplayItem> SortSoftwareByInstallDate(List<SoftwareDisplayItem> items)
    {
        try
        {
            var sortedItems = items.OrderByDescending(software =>
            {
                return ParseInstallDate(software.安装日期, software.软件名称);
            }).ToList();

            System.Diagnostics.Debug.WriteLine($"软件列表已按安装时间排序，最新安装的软件排在前面");

            // 输出前5个软件的安装日期用于调试
            for (int i = 0; i < Math.Min(5, sortedItems.Count); i++)
            {
                System.Diagnostics.Debug.WriteLine($"排序后第{i+1}位: {sortedItems[i].软件名称} - {sortedItems[i].安装日期}");
            }

            return sortedItems;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"排序失败: {ex.Message}");
            // 如果排序失败，返回原始列表
            return items;
        }
    }

    // 解析安装日期
    private DateTime ParseInstallDate(string dateString, string softwareName)
    {
        if (string.IsNullOrEmpty(dateString) || dateString == "未知")
        {
            return DateTime.MinValue;
        }

        // 尝试标准解析
        if (DateTime.TryParse(dateString, out DateTime installDate))
        {
            return installDate;
        }

        // 尝试常见的日期格式
        string[] formats = {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "MM/dd/yyyy",
            "dd/MM/yyyy",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss"
        };

        foreach (string format in formats)
        {
            if (DateTime.TryParseExact(dateString, format, null, System.Globalization.DateTimeStyles.None, out installDate))
            {
                return installDate;
            }
        }

        // 如果所有格式都失败，记录调试信息并返回最小值
        System.Diagnostics.Debug.WriteLine($"无法解析安装日期: '{dateString}' for {softwareName}");
        return DateTime.MinValue;
    }

    // 执行搜索筛选（内部方法，带性能优化）
    private void PerformSearchInternal(string searchText)
    {
        try
        {
            // 防止重复搜索
            if (_isSearching)
            {
                System.Diagnostics.Debug.WriteLine("搜索正在进行中，跳过此次搜索");
                return;
            }

            _isSearching = true;

            List<SoftwareDisplayItem> filteredItems;

            if (string.IsNullOrEmpty(searchText))
            {
                // 如果搜索文本为空，应用当前分类筛选
                filteredItems = GetFilteredItems();
                System.Diagnostics.Debug.WriteLine("显示当前分类的软件");
            }
            else
            {
                // 执行智能搜索，支持中英文和多关键词
                var startTime = System.Diagnostics.Stopwatch.StartNew();
                filteredItems = GetFilteredItems(); // 在当前分类基础上搜索
                startTime.Stop();
                System.Diagnostics.Debug.WriteLine($"搜索 '{searchText}' 找到 {filteredItems.Count} 个结果，耗时 {startTime.ElapsedMilliseconds}ms");
            }

            // 显示筛选后的结果
            DisplaySoftwareList(filteredItems);

            // 更新状态栏显示筛选结果
            var categoryName = GetCategoryName(_currentFilterIndex);
            if (string.IsNullOrEmpty(searchText))
            {
                UpdateStatusBar($"{categoryName}: {filteredItems.Count} 个", $"总计: {_allLoadedItems.Count} 个");
            }
            else
            {
                UpdateStatusBar($"搜索结果: {filteredItems.Count} 个", $"{categoryName}中搜索");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"执行搜索失败: {ex.Message}");
        }
        finally
        {
            _isSearching = false;
        }
    }

    // 执行智能搜索
    private List<SoftwareDisplayItem> PerformSmartSearch(string searchText)
    {
        try
        {
            // 将搜索文本分割为多个关键词
            var keywords = searchText.ToLower()
                .Split(new char[] { ' ', ',', ';', '\t' }, StringSplitOptions.RemoveEmptyEntries)
                .Where(k => !string.IsNullOrWhiteSpace(k))
                .ToList();

            if (!keywords.Any())
                return _allSoftwareItems;

            return _allSoftwareItems.Where(software =>
            {
                // 为每个软件创建搜索文本
                var searchableText = $"{software.软件名称} {software.发布商} {software.安装路径} {software.版本}".ToLower();

                // 检查是否所有关键词都能在搜索文本中找到（AND逻辑）
                return keywords.All(keyword =>
                    searchableText.Contains(keyword) ||
                    // 支持部分匹配：如果关键词长度>=2，支持模糊匹配
                    (keyword.Length >= 2 && ContainsFuzzyMatch(searchableText, keyword))
                );
            }).ToList();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"智能搜索失败: {ex.Message}");
            return _allSoftwareItems;
        }
    }

    // 模糊匹配检查
    private bool ContainsFuzzyMatch(string text, string keyword)
    {
        try
        {
            // 简单的模糊匹配：检查关键词的字符是否按顺序出现在文本中
            int keywordIndex = 0;
            for (int i = 0; i < text.Length && keywordIndex < keyword.Length; i++)
            {
                if (text[i] == keyword[keywordIndex])
                {
                    keywordIndex++;
                }
            }
            return keywordIndex == keyword.Length;
        }
        catch
        {
            return false;
        }
    }

    // 更新搜索状态栏
    private void UpdateSearchStatusBar(int filteredCount, int totalCount, string searchText)
    {
        try
        {
            string countText;
            if (string.IsNullOrEmpty(searchText))
            {
                countText = $"已安装软件: {totalCount} 个";
            }
            else
            {
                countText = $"搜索结果: {filteredCount} 个 (共 {totalCount} 个)";
            }

            if (this.FindControl<TextBlock>("SoftwareCountText") is TextBlock countTextBlock)
            {
                countTextBlock.Text = countText;
            }

            System.Diagnostics.Debug.WriteLine($"搜索状态栏更新: {countText}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新搜索状态栏失败: {ex.Message}");
        }
    }

    private void UpdateStatusBar(int softwareCount, long totalSize)
    {
        try
        {
            // 查找状态栏中的TextBlock并更新
            if (this.FindControl<TextBlock>("SoftwareCountText") is TextBlock countText)
            {
                countText.Text = $"已安装软件: {softwareCount} 个";
            }

            if (this.FindControl<TextBlock>("TotalSizeText") is TextBlock sizeText)
            {
                sizeText.Text = $"总大小: {_scanner.FormatFileSize(totalSize)}";
            }

            System.Diagnostics.Debug.WriteLine($"状态栏更新：{softwareCount} 个软件，总大小 {_scanner.FormatFileSize(totalSize)}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新状态栏失败: {ex.Message}");
        }
    }

    // 重载方法：支持字符串参数的状态栏更新
    private void UpdateStatusBar(string countText, string sizeText)
    {
        try
        {
            // 查找状态栏中的TextBlock并更新
            if (this.FindControl<TextBlock>("SoftwareCountText") is TextBlock countTextBlock)
            {
                countTextBlock.Text = countText;
            }

            if (this.FindControl<TextBlock>("TotalSizeText") is TextBlock sizeTextBlock)
            {
                sizeTextBlock.Text = sizeText;
            }

            System.Diagnostics.Debug.WriteLine($"状态栏更新：{countText}, {sizeText}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新状态栏失败: {ex.Message}");
        }
    }

    private Avalonia.Controls.Control CreateSoftwareIcon(string iconPath, string softwareName = "")
    {
        try
        {
            // 如果有图标路径，尝试加载真实图标
            if (!string.IsNullOrEmpty(iconPath))
            {
                var image = new Avalonia.Controls.Image
                {
                    Width = 16,
                    Height = 16,
                    Margin = new Avalonia.Thickness(0, 0, 8, 0),
                    VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
                };

                try
                {
                    // 处理图标路径，可能包含参数
                    var actualIconPath = ExtractIconPath(iconPath);
                    System.Diagnostics.Debug.WriteLine($"尝试加载图标: {softwareName} -> {actualIconPath}");

                    if (!string.IsNullOrEmpty(actualIconPath))
                    {
                        // 尝试多种方式加载图标
                        var bitmap = TryLoadIcon(actualIconPath, iconPath);
                        if (bitmap != null)
                        {
                            image.Source = bitmap;
                            System.Diagnostics.Debug.WriteLine($"成功加载图标: {softwareName}");
                            return image;
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 图标加载失败，记录调试信息
                    System.Diagnostics.Debug.WriteLine($"图标加载失败: {softwareName} -> {iconPath}, 错误: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"创建图标时出错: {ex.Message}");
        }

        // 返回默认图标，根据软件类型选择不同图标
        var defaultIcon = GetDefaultIconForSoftware(iconPath, softwareName);
        System.Diagnostics.Debug.WriteLine($"使用默认图标: {softwareName} -> {defaultIcon}");
        return new TextBlock
        {
            Text = defaultIcon,
            FontSize = 16,
            Margin = new Avalonia.Thickness(0, 0, 8, 0),
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
        };
    }

    // 尝试多种方式加载图标
    private Avalonia.Media.Imaging.Bitmap? TryLoadIcon(string iconPath, string originalPath)
    {
        try
        {
            // 方法1: 直接加载文件（如果是图片文件）
            if (System.IO.File.Exists(iconPath))
            {
                var extension = System.IO.Path.GetExtension(iconPath).ToLower();
                if (extension == ".ico" || extension == ".png" || extension == ".jpg" || extension == ".jpeg" || extension == ".bmp")
                {
                    return new Avalonia.Media.Imaging.Bitmap(iconPath);
                }
            }

            // 方法2: 如果是exe文件，尝试提取图标
            if (iconPath.EndsWith(".exe", StringComparison.OrdinalIgnoreCase) && System.IO.File.Exists(iconPath))
            {
                return ExtractIconFromExecutable(iconPath);
            }

            // 方法3: 处理带索引的图标路径
            if (originalPath.Contains(','))
            {
                var parts = originalPath.Split(',');
                if (parts.Length >= 2 && int.TryParse(parts[1].Trim(), out int iconIndex))
                {
                    var filePath = ExtractIconPath(parts[0]);
                    if (System.IO.File.Exists(filePath))
                    {
                        return ExtractIconFromFile(filePath, iconIndex);
                    }
                }
            }

            // 方法4: 尝试在安装目录中查找图标
            return FindIconInInstallDirectory(iconPath);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载图标时出错: {iconPath}, 错误: {ex.Message}");
            return null;
        }
    }

    private string ExtractIconPath(string iconPath)
    {
        if (string.IsNullOrEmpty(iconPath))
            return "";

        // 移除引号
        iconPath = iconPath.Trim('"');

        // 如果包含逗号，可能是 "path,index" 格式
        if (iconPath.Contains(','))
        {
            var parts = iconPath.Split(',');
            iconPath = parts[0].Trim();
        }

        // 展开环境变量
        iconPath = System.Environment.ExpandEnvironmentVariables(iconPath);

        return iconPath;
    }

    // 从可执行文件提取图标
    private Avalonia.Media.Imaging.Bitmap? ExtractIconFromExecutable(string exePath)
    {
        try
        {
            // 简单的方法：尝试查找同目录下的图标文件
            var directory = System.IO.Path.GetDirectoryName(exePath);
            var fileName = System.IO.Path.GetFileNameWithoutExtension(exePath);

            if (!string.IsNullOrEmpty(directory))
            {
                // 查找常见的图标文件
                var iconExtensions = new[] { ".ico", ".png", ".jpg", ".jpeg", ".bmp" };
                foreach (var ext in iconExtensions)
                {
                    var iconFile = System.IO.Path.Combine(directory, fileName + ext);
                    if (System.IO.File.Exists(iconFile))
                    {
                        return new Avalonia.Media.Imaging.Bitmap(iconFile);
                    }
                }

                // 查找通用图标文件
                var commonIconNames = new[] { "icon", "app", "logo", "main" };
                foreach (var name in commonIconNames)
                {
                    foreach (var ext in iconExtensions)
                    {
                        var iconFile = System.IO.Path.Combine(directory, name + ext);
                        if (System.IO.File.Exists(iconFile))
                        {
                            return new Avalonia.Media.Imaging.Bitmap(iconFile);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"从可执行文件提取图标失败: {exePath}, 错误: {ex.Message}");
        }
        return null;
    }

    // 从文件提取指定索引的图标
    private Avalonia.Media.Imaging.Bitmap? ExtractIconFromFile(string filePath, int iconIndex)
    {
        try
        {
            // 对于简单实现，如果索引为0，尝试直接加载文件
            if (iconIndex == 0 && System.IO.File.Exists(filePath))
            {
                var extension = System.IO.Path.GetExtension(filePath).ToLower();
                if (extension == ".ico" || extension == ".png" || extension == ".jpg" || extension == ".jpeg" || extension == ".bmp")
                {
                    return new Avalonia.Media.Imaging.Bitmap(filePath);
                }
                else if (extension == ".exe" || extension == ".dll")
                {
                    return ExtractIconFromExecutable(filePath);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"从文件提取图标失败: {filePath}[{iconIndex}], 错误: {ex.Message}");
        }
        return null;
    }

    // 在安装目录中查找图标
    private Avalonia.Media.Imaging.Bitmap? FindIconInInstallDirectory(string iconPath)
    {
        try
        {
            var directory = System.IO.Path.GetDirectoryName(iconPath);
            if (string.IsNullOrEmpty(directory) || !System.IO.Directory.Exists(directory))
                return null;

            // 查找目录中的图标文件
            var iconExtensions = new[] { "*.ico", "*.png", "*.jpg", "*.jpeg", "*.bmp" };
            foreach (var pattern in iconExtensions)
            {
                var files = System.IO.Directory.GetFiles(directory, pattern, System.IO.SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    // 优先选择名称包含icon、app、logo的文件
                    var priorityFile = files.FirstOrDefault(f =>
                    {
                        var name = System.IO.Path.GetFileNameWithoutExtension(f).ToLower();
                        return name.Contains("icon") || name.Contains("app") || name.Contains("logo");
                    });

                    var selectedFile = priorityFile ?? files[0];
                    return new Avalonia.Media.Imaging.Bitmap(selectedFile);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"在安装目录查找图标失败: {iconPath}, 错误: {ex.Message}");
        }
        return null;
    }

    private string GetDefaultIconForSoftware(string iconPath, string softwareName)
    {
        var searchText = $"{iconPath} {softwareName}".ToLower();

        // 根据软件名称和路径特征选择合适的图标

        // Microsoft 相关软件
        if (searchText.Contains("microsoft") || searchText.Contains("windows"))
        {
            if (searchText.Contains("edge")) return "🌐";
            if (searchText.Contains("office") || searchText.Contains("word") || searchText.Contains("excel") || searchText.Contains("powerpoint")) return "📄";
            if (searchText.Contains("visual studio") || searchText.Contains("vscode")) return "💻";
            if (searchText.Contains(".net") || searchText.Contains("runtime") || searchText.Contains("framework")) return "⚙️";
            return "🪟";
        }

        // 浏览器
        if (searchText.Contains("chrome") || searchText.Contains("firefox") || searchText.Contains("browser") || searchText.Contains("edge"))
            return "🌐";

        // 开发工具
        if (searchText.Contains("visual studio") || searchText.Contains("code") || searchText.Contains("vscode") ||
            searchText.Contains("intellij") || searchText.Contains("eclipse") || searchText.Contains("android studio") ||
            searchText.Contains("git") || searchText.Contains("python") || searchText.Contains("node") || searchText.Contains("java"))
            return "💻";

        // 办公软件
        if (searchText.Contains("office") || searchText.Contains("word") || searchText.Contains("excel") ||
            searchText.Contains("powerpoint") || searchText.Contains("wps") || searchText.Contains("pdf"))
            return "📄";

        // 游戏相关
        if (searchText.Contains("game") || searchText.Contains("steam") || searchText.Contains("epic") ||
            searchText.Contains("origin") || searchText.Contains("battle.net") || searchText.Contains("游戏"))
            return "🎮";
        else if (searchText.Contains("media") || searchText.Contains("player") || searchText.Contains("vlc") || searchText.Contains("音乐") || searchText.Contains("视频"))
            return "🎵";
        else if (searchText.Contains("adobe") || searchText.Contains("photoshop") || searchText.Contains("设计"))
            return "🎨";
        else if (searchText.Contains("java"))
            return "☕";
        else if (searchText.Contains("python"))
            return "🐍";
        else if (searchText.Contains("node") || searchText.Contains("npm"))
            return "📗";
        else if (searchText.Contains("qq") || searchText.Contains("微信") || searchText.Contains("wechat"))
            return "💬";
        else if (searchText.Contains("安全") || searchText.Contains("杀毒") || searchText.Contains("360"))
            return "🛡️";
        else if (searchText.Contains("压缩") || searchText.Contains("zip") || searchText.Contains("rar"))
            return "📁";
        else if (searchText.Contains("输入法"))
            return "⌨️";
        else if (searchText.Contains("驱动") || searchText.Contains("driver"))
            return "🔧";
        else if (searchText.Contains("nvidia") || searchText.Contains("显卡"))
            return "🎮";
        else
            return "📦";
    }

    // 行点击事件处理（优化版本 - 无需刷新整个列表）
    private void OnRowClicked(SoftwareDisplayItem software, Border rowBorder)
    {
        try
        {
            // 取消之前选中的项目（只更新UI，不刷新整个列表）
            if (_selectedItem != null && _selectedItem != software)
            {
                _selectedItem.IsSelected = false;

                // 更新之前选中行的背景和复选框
                if (_selectedRowBorder != null)
                {
                    UpdateRowBackground(_selectedRowBorder, false);
                }
                if (_selectedCheckBox != null)
                {
                    _selectedCheckBox.IsChecked = false;
                }
            }

            // 切换当前项目的选中状态
            software.IsSelected = !software.IsSelected;

            // 更新选中项目和UI引用
            if (software.IsSelected)
            {
                _selectedItem = software;
                _selectedRowBorder = rowBorder;
                // 查找当前行的复选框
                _selectedCheckBox = FindCheckBoxInRow(rowBorder);
                if (_selectedCheckBox != null)
                {
                    _selectedCheckBox.IsChecked = true;
                }
            }
            else
            {
                _selectedItem = null;
                _selectedRowBorder = null;
                _selectedCheckBox = null;
            }

            // 只更新当前行的背景色，无需刷新整个列表
            UpdateRowBackground(rowBorder, software.IsSelected);

            System.Diagnostics.Debug.WriteLine($"选中状态变更: {software.软件名称} - {software.IsSelected}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"行点击处理失败: {ex.Message}");
        }
    }

    // 复选框点击事件处理（优化版本 - 无需刷新整个列表）
    private void OnCheckBoxClicked(SoftwareDisplayItem software, Border rowBorder, CheckBox checkBox)
    {
        try
        {
            // 取消之前选中的项目（只更新UI，不刷新整个列表）
            if (_selectedItem != null && _selectedItem != software)
            {
                _selectedItem.IsSelected = false;

                // 更新之前选中行的背景和复选框
                if (_selectedRowBorder != null)
                {
                    UpdateRowBackground(_selectedRowBorder, false);
                }
                if (_selectedCheckBox != null)
                {
                    _selectedCheckBox.IsChecked = false;
                }
            }

            // 更新选中状态
            software.IsSelected = checkBox.IsChecked == true;

            // 更新选中项目和UI引用
            if (software.IsSelected)
            {
                _selectedItem = software;
                _selectedRowBorder = rowBorder;
                _selectedCheckBox = checkBox;
            }
            else
            {
                _selectedItem = null;
                _selectedRowBorder = null;
                _selectedCheckBox = null;
            }

            // 只更新当前行的背景色，无需刷新整个列表
            UpdateRowBackground(rowBorder, software.IsSelected);

            System.Diagnostics.Debug.WriteLine($"复选框状态变更: {software.软件名称} - {software.IsSelected}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"复选框点击处理失败: {ex.Message}");
        }
    }

    // 鼠标进入行事件
    private void OnRowPointerEntered(Border rowBorder, bool isSelected)
    {
        try
        {
            if (!isSelected)
            {
                rowBorder.Background = Avalonia.Media.Brush.Parse("#F5F5F5");  // 悬停时的浅灰色背景
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"鼠标进入行处理失败: {ex.Message}");
        }
    }

    // 鼠标离开行事件
    private void OnRowPointerExited(Border rowBorder, bool isSelected)
    {
        try
        {
            UpdateRowBackground(rowBorder, isSelected);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"鼠标离开行处理失败: {ex.Message}");
        }
    }

    // 更新行背景色
    private void UpdateRowBackground(Border rowBorder, bool isSelected)
    {
        try
        {
            rowBorder.Background = isSelected ?
                Avalonia.Media.Brush.Parse("#E3F2FD") :  // 选中时的蓝色背景
                Avalonia.Media.Brush.Parse("White");     // 默认白色背景
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新行背景失败: {ex.Message}");
        }
    }

    // 在行中查找复选框
    private CheckBox? FindCheckBoxInRow(Border rowBorder)
    {
        try
        {
            if (rowBorder.Child is Grid grid)
            {
                foreach (var child in grid.Children)
                {
                    if (child is CheckBox checkBox)
                    {
                        return checkBox;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"查找复选框失败: {ex.Message}");
        }
        return null;
    }

    // 刷新当前显示（仅在必要时使用，如搜索时）
    private void RefreshCurrentDisplay()
    {
        try
        {
            // 清除选中状态引用，因为要重新创建列表
            _selectedItem = null;
            _selectedRowBorder = null;
            _selectedCheckBox = null;

            // 获取当前显示的项目列表
            var currentDisplayItems = string.IsNullOrEmpty(_lastSearchText) ?
                _allSoftwareItems :
                PerformSmartSearch(_lastSearchText);

            // 重新显示列表
            DisplaySoftwareList(currentDisplayItems);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"刷新当前显示失败: {ex.Message}");
        }
    }

    // 添加项目到显示列表（用于分批加载）
    private void AddItemsToDisplay(List<SoftwareDisplayItem> newItems)
    {
        try
        {
            if (SoftwareListPanel == null) return;

            foreach (var software in newItems)
            {
                var rowBorder = CreateSoftwareRow(software);
                SoftwareListPanel.Children.Add(rowBorder);
            }

            System.Diagnostics.Debug.WriteLine($"添加了 {newItems.Count} 个软件行到显示列表");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加项目到显示列表失败: {ex.Message}");
        }
    }

    // 创建软件行（提取的公共方法）
    private Border CreateSoftwareRow(SoftwareDisplayItem software)
    {
        var rowBorder = new Border
        {
            BorderBrush = Avalonia.Media.Brush.Parse("#F0F0F0"),
            BorderThickness = new Avalonia.Thickness(0, 0, 0, 1),
            Padding = new Avalonia.Thickness(15, 8),
            Background = software.IsSelected ?
                Avalonia.Media.Brush.Parse("#E3F2FD") :
                Avalonia.Media.Brush.Parse("White"),
            Cursor = new Avalonia.Input.Cursor(Avalonia.Input.StandardCursorType.Hand)
        };

        // 为行添加点击事件
        rowBorder.PointerPressed += (sender, e) => OnRowClicked(software, rowBorder);
        rowBorder.PointerEntered += (sender, e) => OnRowPointerEntered(rowBorder, software.IsSelected);
        rowBorder.PointerExited += (sender, e) => OnRowPointerExited(rowBorder, software.IsSelected);

        var rowGrid = new Grid();
        // 列定义
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(40) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(300) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(140) });

        // 创建控件（简化版本，只包含必要元素）
        var checkBox = new CheckBox
        {
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            IsChecked = software.IsSelected
        };
        checkBox.Click += (sender, e) => OnCheckBoxClicked(software, rowBorder, checkBox);
        Grid.SetColumn(checkBox, 0);

        // 软件名称（带图标）
        var namePanel = new StackPanel
        {
            Orientation = Avalonia.Layout.Orientation.Horizontal,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
        };

        // 添加图标
        var iconElement = CreateSoftwareIcon(software.图标路径, software.软件名称);
        namePanel.Children.Add(iconElement);

        var nameText = new TextBlock
        {
            Text = software.软件名称,
            FontSize = 12,
            FontWeight = Avalonia.Media.FontWeight.Medium,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            TextTrimming = Avalonia.Media.TextTrimming.CharacterEllipsis,
            MaxWidth = 220  // 减少宽度为图标留出空间
        };
        namePanel.Children.Add(nameText);
        Grid.SetColumn(namePanel, 1);

        var versionText = new TextBlock
        {
            Text = software.版本,
            FontSize = 11,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            Foreground = Avalonia.Media.Brush.Parse("#6C757D")
        };
        Grid.SetColumn(versionText, 2);

        var sizeText = new TextBlock
        {
            Text = software.大小,
            FontSize = 11,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            Foreground = Avalonia.Media.Brush.Parse("#6C757D")
        };
        Grid.SetColumn(sizeText, 3);

        var dateText = new TextBlock
        {
            Text = software.安装日期,
            FontSize = 11,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            Foreground = Avalonia.Media.Brush.Parse("#6C757D")
        };
        Grid.SetColumn(dateText, 4);

        var pathText = new TextBlock
        {
            Text = software.安装路径,
            FontSize = 11,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            Foreground = Avalonia.Media.Brush.Parse("#6C757D"),
            TextTrimming = Avalonia.Media.TextTrimming.CharacterEllipsis
        };
        Grid.SetColumn(pathText, 5);

        var publisherText = new TextBlock
        {
            Text = software.发布商,
            FontSize = 11,
            VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
            Foreground = Avalonia.Media.Brush.Parse("#6C757D")
        };
        Grid.SetColumn(publisherText, 6);

        // 添加所有控件到网格
        rowGrid.Children.Add(checkBox);
        rowGrid.Children.Add(namePanel);  // 使用包含图标的面板
        rowGrid.Children.Add(versionText);
        rowGrid.Children.Add(sizeText);
        rowGrid.Children.Add(dateText);
        rowGrid.Children.Add(pathText);
        rowGrid.Children.Add(publisherText);

        rowBorder.Child = rowGrid;
        return rowBorder;
    }

    // 分类选择变更事件处理
    private void OnCategorySelectionChanged(object? sender, Avalonia.Controls.SelectionChangedEventArgs e)
    {
        try
        {
            if (sender is ComboBox comboBox)
            {
                _currentFilterIndex = comboBox.SelectedIndex;
                System.Diagnostics.Debug.WriteLine($"分类筛选变更为索引: {_currentFilterIndex}");

                // 应用筛选
                ApplyCurrentFilter();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"分类选择变更处理失败: {ex.Message}");
        }
    }

    // 应用当前筛选
    private void ApplyCurrentFilter()
    {
        try
        {
            if (!_isFullyLoaded)
            {
                System.Diagnostics.Debug.WriteLine("软件尚未完全加载，等待加载完成后再筛选");
                return;
            }

            var filteredItems = GetFilteredItems();
            DisplaySoftwareList(filteredItems);

            // 更新状态栏
            var categoryName = GetCategoryName(_currentFilterIndex);
            UpdateStatusBar($"{categoryName}: {filteredItems.Count} 个", $"总计: {_allLoadedItems.Count} 个");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"应用筛选失败: {ex.Message}");
        }
    }

    // 获取筛选后的项目
    private List<SoftwareDisplayItem> GetFilteredItems()
    {
        var baseItems = string.IsNullOrEmpty(_lastSearchText) ?
            _allLoadedItems :
            PerformSmartSearch(_lastSearchText);

        return _currentFilterIndex switch
        {
            0 => baseItems, // 全部软件
            1 => baseItems.Where(s => !IsSystemSoftware(s.软件名称)).ToList(), // 用户软件
            2 => baseItems.Where(s => IsSystemSoftware(s.软件名称)).ToList(), // 系统软件
            3 => baseItems.Where(s => IsGameSoftware(s.软件名称, s.发布商)).ToList(), // 游戏软件
            4 => baseItems.Where(s => IsDevelopmentSoftware(s.软件名称, s.发布商)).ToList(), // 开发工具
            5 => baseItems.Where(s => IsOfficeSoftware(s.软件名称, s.发布商)).ToList(), // 办公软件
            6 => baseItems.Where(s => IsEngineeringSoftware(s.软件名称, s.发布商)).ToList(), // 工程软件
            _ => baseItems
        };
    }

    // 计算总大小
    private string CalculateTotalSize()
    {
        try
        {
            // 这里可以实现实际的大小计算逻辑
            return "计算中...";
        }
        catch
        {
            return "未知";
        }
    }

    // 获取分类名称（与ComboBox项目完全对应）
    private string GetCategoryName(int index)
    {
        return index switch
        {
            0 => "全部软件",
            1 => "用户软件",
            2 => "系统软件",
            3 => "游戏软件",
            4 => "开发工具",
            5 => "办公软件",
            6 => "工程软件",
            _ => "全部软件"
        };
    }

    // 软件分类判断方法
    private bool IsSystemSoftware(string name)
    {
        var lowerName = name.ToLower();
        return lowerName.Contains("windows") ||
               lowerName.Contains("microsoft") && (
                   lowerName.Contains("runtime") ||
                   lowerName.Contains("redistributable") ||
                   lowerName.Contains(".net") ||
                   lowerName.Contains("framework") ||
                   lowerName.Contains("asp.net") ||
                   lowerName.Contains("desktop") ||
                   lowerName.Contains("targeting") ||
                   lowerName.Contains("manifest")
               ) ||
               lowerName.Contains("intel") ||
               lowerName.Contains("amd") ||
               lowerName.Contains("nvidia") ||
               lowerName.Contains("driver") ||
               lowerName.Contains("runtime") ||
               lowerName.Contains("redistributable") ||
               lowerName.Contains("update for") ||
               lowerName.Contains("hotfix") ||
               lowerName.StartsWith("kb") ||
               lowerName.Contains("security update");
    }

    private bool IsGameSoftware(string name, string publisher)
    {
        var lowerName = name.ToLower();
        var lowerPublisher = publisher.ToLower();
        return lowerName.Contains("game") ||
               lowerName.Contains("steam") ||
               lowerName.Contains("epic games") ||
               lowerName.Contains("origin") ||
               lowerName.Contains("battle.net") ||
               lowerName.Contains("uplay") ||
               lowerName.Contains("gog") ||
               lowerName.Contains("minecraft") ||
               lowerName.Contains("league of legends") ||
               lowerName.Contains("world of warcraft") ||
               lowerPublisher.Contains("valve") ||
               lowerPublisher.Contains("epic games") ||
               lowerPublisher.Contains("electronic arts") ||
               lowerPublisher.Contains("blizzard") ||
               lowerPublisher.Contains("riot games") ||
               lowerPublisher.Contains("cd projekt");
    }

    private bool IsDevelopmentSoftware(string name, string publisher)
    {
        var lowerName = name.ToLower();
        var lowerPublisher = publisher.ToLower();

        // 排除系统运行时组件
        if (IsSystemSoftware(name)) return false;

        return lowerName.Contains("visual studio") && !lowerName.Contains("redistributable") ||
               lowerName.Contains("vs code") ||
               lowerName.Contains("code") && lowerPublisher.Contains("microsoft") ||
               lowerName.Contains("intellij") ||
               lowerName.Contains("eclipse") ||
               lowerName.Contains("android studio") ||
               lowerName.Contains("git") && !lowerName.Contains("digit") ||
               lowerName.Contains("python") ||
               lowerName.Contains("node.js") ||
               lowerName.Contains("nodejs") ||
               lowerName.Contains("docker") ||
               lowerName.Contains("postman") ||
               lowerName.Contains("sublime") ||
               lowerName.Contains("notepad++") ||
               lowerName.Contains("webstorm") ||
               lowerName.Contains("pycharm") ||
               lowerName.Contains("phpstorm") ||
               lowerName.Contains("datagrip") ||
               lowerName.Contains("rider") ||
               lowerName.Contains("clion");
    }

    private bool IsOfficeSoftware(string name, string publisher)
    {
        var lowerName = name.ToLower();
        var lowerPublisher = publisher.ToLower();

        // 排除系统组件
        if (IsSystemSoftware(name)) return false;

        return lowerName.Contains("office") && !lowerName.Contains("libreoffice") ||
               lowerName.Contains("word") ||
               lowerName.Contains("excel") ||
               lowerName.Contains("powerpoint") ||
               lowerName.Contains("outlook") ||
               lowerName.Contains("onenote") ||
               lowerName.Contains("access") ||
               lowerName.Contains("publisher") && lowerPublisher.Contains("microsoft") ||
               lowerName.Contains("wps") ||
               lowerName.Contains("libreoffice") ||
               lowerName.Contains("openoffice") ||
               lowerName.Contains("adobe acrobat") ||
               lowerName.Contains("adobe reader") ||
               lowerName.Contains("foxit") ||
               lowerName.Contains("pdf") && (lowerName.Contains("reader") || lowerName.Contains("editor")) ||
               lowerName.Contains("notion") ||
               lowerName.Contains("evernote") ||
               lowerName.Contains("typora");
    }

    private bool IsEngineeringSoftware(string name, string publisher)
    {
        var lowerName = name.ToLower();
        var lowerPublisher = publisher.ToLower();

        return lowerName.Contains("autocad") ||
               lowerName.Contains("solidworks") ||
               lowerName.Contains("catia") ||
               lowerName.Contains("revit") ||
               lowerName.Contains("matlab") ||
               lowerName.Contains("ansys") ||
               lowerName.Contains("inventor") ||
               lowerName.Contains("fusion 360") ||
               lowerName.Contains("sketchup") ||
               lowerName.Contains("rhino") ||
               lowerName.Contains("3ds max") ||
               lowerName.Contains("maya") ||
               lowerName.Contains("blender") ||
               lowerName.Contains("cinema 4d") ||
               lowerName.Contains("keyshot") ||
               lowerName.Contains("altium") ||
               lowerName.Contains("eagle") ||
               lowerName.Contains("kicad") ||
               lowerName.Contains("proteus") ||
               lowerName.Contains("labview") ||
               lowerPublisher.Contains("autodesk") ||
               lowerPublisher.Contains("dassault") ||
               lowerPublisher.Contains("siemens") ||
               lowerPublisher.Contains("ptc") ||
               lowerPublisher.Contains("mathworks");
    }

    // 资源清理
    protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
    {
        try
        {
            _searchTimer?.Stop();
            _searchTimer?.Dispose();
            _searchTimer = null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"资源清理失败: {ex.Message}");
        }

        base.OnDetachedFromVisualTree(e);
    }
}
