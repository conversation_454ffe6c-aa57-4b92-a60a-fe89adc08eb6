# 桌面快捷方式管理功能说明

## 功能概述

桌面快捷方式管理模块实现了类似参考界面的应用启动器功能，支持拖拽添加应用程序和双击启动。

**重要特性**：应用启动时不会自动加载任何桌面图标，所有显示的图标都必须通过用户拖拽操作添加。

## 主要特性

### 1. 拖拽添加功能
- **支持的文件类型**：`.exe`, `.lnk`, `.bat`, `.cmd`
- **操作方式**：直接将应用程序文件拖拽到界面中央区域
- **自动分类**：根据应用程序名称自动分配到相应分类
- **真实图标提取**：自动提取 `.exe` 文件的真实图标并显示
- **图标缓存**：提取的图标会缓存到临时目录，提高加载速度

### 2. 分类管理
- **全部**：显示所有应用
- **PDF相关**：PDF阅读器、编辑器等
- **创作**：Photoshop、Illustrator、Premiere等创作工具
- **开发工具**：Visual Studio、VS Code、Git等开发环境
- **系统工具**：任务管理器、控制面板等系统工具
- **游戏**：Steam、Epic Games等游戏平台

### 3. 双击启动
- 双击应用图标即可启动对应程序
- 支持带参数的应用程序启动
- 自动记录最后访问时间

### 4. 搜索功能
- 实时搜索应用程序名称
- 支持模糊匹配

## 界面设计

### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ 🚀 应用启动器                                            │
├─────────────────────────────────────────────────────────┤
│ 拖入应用图标，双击启动应用程序。                          │
├─────────────────────────────────────────────────────────┤
│ [全部] [PDF相关] [创作] [开发工具] [系统工具] [游戏]  🔍搜索 │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌───┐  ┌───┐  ┌───┐  ┌───┐  ┌───┐  ┌───┐              │
│  │📱 │  │🌐 │  │📄 │  │💻 │  │🎮 │  │⚙️ │              │
│  │App│  │Web│  │Doc│  │Dev│  │Game│ │Tool│              │
│  └───┘  └───┘  └───┘  └───┘  └───┘  └───┘              │
│                                                         │
│  ┌───┐  ┌───┐  ┌───┐  ┌───┐                            │
│  │🎨 │  │📊 │  │🎵 │  │📧 │                            │
│  │Art│  │Data│ │Music│ │Mail│                            │
│  └───┘  └───┘  └───┘  └───┘                            │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 空状态界面
应用启动时默认显示空状态界面：
- 大型拖拽区域图标（圆形边框）
- 清晰的操作说明："拖拽应用程序(.exe)到此处添加快捷方式"
- 支持的文件类型提示：".exe, .lnk, .bat, .cmd"
- 友好的视觉设计，引导用户进行拖拽操作

## 技术实现

### 核心组件
1. **ShortcutService**：快捷方式管理服务
2. **DesktopShortcutsViewModel**：视图模型，处理业务逻辑
3. **DesktopShortcutsView**：用户界面，处理拖拽和双击事件

### 拖拽实现
```csharp
// 设置拖拽支持
DragDrop.SetAllowDrop(dropArea, true);
dropArea.AddHandler(DragDrop.DragOverEvent, OnDragOver);
dropArea.AddHandler(DragDrop.DropEvent, OnDrop);
```

### 图标分类逻辑
根据应用程序名称关键词自动分类：
- PDF相关：包含 "pdf", "acrobat", "foxit" 等
- 创作工具：包含 "photoshop", "illustrator", "premiere" 等
- 开发工具：包含 "visual", "code", "git" 等
- 系统工具：包含 "windows", "control", "task" 等
- 游戏：包含 "steam", "epic", "game" 等

### 图标显示
支持两种图标显示模式：

**真实图标模式**（优先）：
- 自动提取 `.exe` 文件的内嵌图标
- 显示应用程序的真实图标，与系统一致
- 图标缓存到 `%TEMP%\LSSOFT\Icons\` 目录

**备用 Emoji 图标模式**：
当无法提取真实图标时，使用 Emoji 图标：
- 🌐 浏览器类应用
- 📄 文档类应用
- 💻 开发工具
- 🎮 游戏应用
- ⚙️ 系统工具
- 🎨 创作工具

## 使用方法

### 添加应用
1. 打开文件资源管理器
2. 找到要添加的应用程序（.exe文件）
3. 将文件拖拽到应用启动器界面
4. 应用会自动添加并分类

### 启动应用
1. 在界面中找到要启动的应用图标
2. 双击图标即可启动应用程序

### 分类筛选
1. 点击顶部的分类按钮
2. 界面会只显示该分类下的应用

### 搜索应用
1. 在右上角搜索框中输入应用名称
2. 界面会实时筛选匹配的应用

## 注意事项

1. **不自动加载**：应用启动时不会自动扫描或加载桌面图标
2. **仅拖拽添加**：所有应用图标都必须通过拖拽操作手动添加
3. **文件权限**：确保有权限访问要添加的应用程序文件
4. **文件路径**：建议使用绝对路径的应用程序文件
5. **重复添加**：系统会自动检测并阻止重复添加相同应用
6. **启动失败**：如果应用启动失败，请检查文件是否存在且有执行权限

## 扩展功能

未来可以考虑添加的功能：
- 自定义图标
- 应用程序分组
- 快捷键启动
- 使用频率统计
- 右键菜单操作
- 应用程序信息显示
