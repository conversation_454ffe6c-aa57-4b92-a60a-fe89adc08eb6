using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace LSSOFT.Converters;

/// <summary>
/// 分类名称到颜色的转换器
/// </summary>
public class CategoryToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not string categoryName)
            return Brushes.Gray;

        return categoryName switch
        {
            "全部" => Brushes.DarkSlateGray,
            "系统" => Brushes.RoyalBlue,
            "办公" => Brushes.Green,
            "开发" => Brushes.Purple,
            "设计" => Brushes.Orange,
            "游戏" => Brushes.Red,
            "手机" => Brushes.DeepPink,
            "其它" => Brushes.Brown,
            "未分类" => Brushes.Gray,
            _ => Brushes.Teal // 自定义分类使用青色
        };
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
