<UserControl
    x:Class="LSSOFT.Views.Modules.InstalledSoftwareView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="using:LSSOFT.Models"
    xmlns:vm="using:LSSOFT.ViewModels"
    d:DesignWidth="1200" d:DesignHeight="700"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题栏  -->
        <Border Grid.Row="0"
            Padding="15,10" Background="#F8F9FA"
            BorderBrush="#E9ECEF" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,8,0" VerticalAlignment="Center"
                    Text="📦" FontSize="25" />
                <TextBlock
                    VerticalAlignment="Center" Text="卸载清理软件"
                    FontSize="24" FontWeight="SemiBold" />
                <TextBlock
                    VerticalAlignment="Center"
                    Text=" ——卸载、重装、注册、详细查看等多功能 " FontSize="16"
                    Foreground="#6C757D" />
            </StackPanel>
        </Border>

        <!--  工具栏  -->
        <Border Grid.Row="1"
            Padding="15,8" Background="White"
            BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  左侧工具栏  -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="5">
                    <Button
                        Name="RefreshButton" Width="32"
                        Height="32" Padding="1"
                        Content="🔄" ToolTip.Tip="刷新"
                        FontSize="20" Click="RefreshButton_Click" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="📝"
                        ToolTip.Tip="注册" FontSize="20" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="🗑️"
                        ToolTip.Tip="卸载" FontSize="20" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="📄"
                        ToolTip.Tip="重命名" FontSize="20" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="📊"
                        ToolTip.Tip="详细信息" FontSize="20" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="🔧"
                        ToolTip.Tip="功能" FontSize="20" />

                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="📋"
                        ToolTip.Tip="查看" FontSize="20" />
                    <Button
                        Width="32" Height="32"
                        Padding="1" Content="🔧"
                        ToolTip.Tip="选项" FontSize="20" />

                </StackPanel>

                <!--  右侧工具栏  -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="8">

                    <Grid Width="200" Height="32">
                        <TextBox
                            Name="SearchTextBox"
                            VerticalContentAlignment="Center"
                            Watermark="搜索软件名称、发布商、路径..." FontSize="12"
                            TextChanged="SearchTextBox_TextChanged" />
                        <Button
                            Name="ClearSearchButton" Width="24"
                            Height="24" Margin="0,0,4,0"
                            HorizontalAlignment="Right" Background="#F5F5F5"
                            BorderThickness="1" BorderBrush="#DDD"
                            CornerRadius="12" Content="✕"
                            FontSize="12" FontWeight="Bold"
                            Foreground="#666" IsVisible="False"
                            Click="ClearSearchButton_Click" ToolTip.Tip="清除搜索">
                            <Button.Styles>
                                <Style Selector="Button:pointerover">
                                    <Setter Property="Background" Value="#E0E0E0" />
                                    <Setter Property="Foreground" Value="#333" />
                                </Style>
                                <Style Selector="Button:pressed">
                                    <Setter Property="Background" Value="#D0D0D0" />
                                </Style>
                            </Button.Styles>
                        </Button>
                    </Grid>
                    <ComboBox
                        Name="CategoryComboBox" Width="100"
                        Height="32" SelectedIndex="0"
                        FontSize="12">
                        <ComboBoxItem Content="全部软件" />
                        <ComboBoxItem Content="用户软件" />
                        <ComboBoxItem Content="系统软件" />
                        <ComboBoxItem Content="游戏软件" />
                        <ComboBoxItem Content="开发工具" />
                        <ComboBoxItem Content="办公软件" />
                        <ComboBoxItem Content="工程软件" />
                    </ComboBox>

                </StackPanel>
            </Grid>
        </Border>

        <!--  软件列表  -->
        <Border Grid.Row="2"
            Margin="5,0,5,5" Background="White"
            BorderBrush="Transparent" BorderThickness="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  固定表头 - 按照软件名称、版本、大小、安装日期、安装路径、发布商的顺序  -->
                <Border Grid.Row="0"
                    Padding="15,8" Background="#F1F3F4"
                    BorderBrush="Transparent" BorderThickness="0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40" />
                            <!--  复选框  -->
                            <ColumnDefinition Width="300" />
                            <!--  软件名称  -->
                            <ColumnDefinition Width="120" />
                            <!--  版本  -->
                            <ColumnDefinition Width="100" />
                            <!--  大小  -->
                            <ColumnDefinition Width="120" />
                            <!--  安装日期  -->
                            <ColumnDefinition Width="*" />
                            <!--  安装路径  -->
                            <ColumnDefinition Width="140" />
                            <!--  发布商  -->
                        </Grid.ColumnDefinitions>
                        <CheckBox Grid.Column="0" VerticalAlignment="Center" />
                        <TextBlock Grid.Column="1"
                            VerticalAlignment="Center" Text="软件名称"
                            FontWeight="SemiBold" FontSize="12" />
                        <TextBlock Grid.Column="2"
                            VerticalAlignment="Center" Text="版本"
                            FontWeight="SemiBold" FontSize="12" />
                        <TextBlock Grid.Column="3"
                            VerticalAlignment="Center" Text="大小"
                            FontWeight="SemiBold" FontSize="12" />
                        <TextBlock Grid.Column="4"
                            VerticalAlignment="Center" Text="安装日期"
                            FontWeight="SemiBold" FontSize="12" />
                        <TextBlock Grid.Column="5"
                            VerticalAlignment="Center" Text="安装路径"
                            FontWeight="SemiBold" FontSize="12" />
                        <TextBlock Grid.Column="6"
                            VerticalAlignment="Center" Text="发布商"
                            FontWeight="SemiBold" FontSize="12" />
                    </Grid>
                </Border>

                <!--  可滚动的数据区域  -->
                <ScrollViewer Grid.Row="1">
                    <StackPanel Name="SoftwareListPanel" Background="White">
                        <!--  数据行将在代码中动态添加  -->
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>

        <!--  状态栏  -->
        <Border Grid.Row="3"
            MinHeight="45" Padding="15,12"
            Background="#F8F9FA" BorderBrush="#DEE2E6"
            BorderThickness="0,1,0,0">
            <Grid VerticalAlignment="Center">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal" Spacing="25">

                    <TextBlock
                        Name="SoftwareCountText"
                        VerticalAlignment="Center" Text="正在扫描..."
                        FontSize="14" Foreground="#6C757D" />
                    <TextBlock
                        Name="TotalSizeText" VerticalAlignment="Center"
                        Text="计算中..." FontSize="14"
                        Foreground="#6C757D" />
                </StackPanel>

                <StackPanel Grid.Column="1"
                    VerticalAlignment="Center"
                    Orientation="Horizontal" Spacing="15">
                    <TextBlock
                        VerticalAlignment="Center" Text="就绪"
                        FontSize="14" FontWeight="Medium"
                        Foreground="#28A745" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>

</UserControl>
