using System;

namespace LSSOFT.Models;

/// <summary>
/// 快捷方式信息模型
/// </summary>
public class ShortcutInfo
{
    /// <summary>
    /// 快捷方式名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 目标路径
    /// </summary>
    public string TargetPath { get; set; } = string.Empty;

    /// <summary>
    /// 快捷方式路径
    /// </summary>
    public string ShortcutPath { get; set; } = string.Empty;

    /// <summary>
    /// 工作目录
    /// </summary>
    public string WorkingDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 参数
    /// </summary>
    public string Arguments { get; set; } = string.Empty;

    /// <summary>
    /// 图标路径
    /// </summary>
    public string IconPath { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 快捷方式类型
    /// </summary>
    public ShortcutType Type { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 最后访问时间
    /// </summary>
    public DateTime LastAccessTime { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 分组/分类
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 是否已选中（用于批量操作）
    /// </summary>
    public bool IsSelected { get; set; }
}

/// <summary>
/// 快捷方式类型枚举
/// </summary>
public enum ShortcutType
{
    /// <summary>
    /// 桌面快捷方式
    /// </summary>
    Desktop,

    /// <summary>
    /// 开始菜单
    /// </summary>
    StartMenu,

    /// <summary>
    /// 快速启动栏
    /// </summary>
    QuickLaunch,

    /// <summary>
    /// 任务栏
    /// </summary>
    Taskbar,

    /// <summary>
    /// 其他
    /// </summary>
    Other
}
