<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4942793" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">游戏，游戏机</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73f;</span>
                <div class="name">卡片形式</div>
                <div class="code-name">&amp;#xe73f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">程序_program1</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">游戏</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">游戏</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">#com-快捷方式</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72e;</span>
                <div class="name">黑客攻击</div>
                <div class="code-name">&amp;#xe72e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe83d;</span>
                <div class="name">自动化</div>
                <div class="code-name">&amp;#xe83d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">盒子程序</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69d;</span>
                <div class="name">自动化</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c4;</span>
                <div class="name">卡片</div>
                <div class="code-name">&amp;#xe6c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe691;</span>
                <div class="name">快捷方式</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe692;</span>
                <div class="name">黑客</div>
                <div class="code-name">&amp;#xe692;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e6;</span>
                <div class="name">快捷方式</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">添加  增加   加</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">卡片形式</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">快捷方式</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c5;</span>
                <div class="name">黑客</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">程序</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87e;</span>
                <div class="name">精简版</div>
                <div class="code-name">&amp;#xe87e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe680;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe680;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">自动化</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xefb1;</span>
                <div class="name">精简内容</div>
                <div class="code-name">&amp;#xefb1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">主页</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">lianfang</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea0a;</span>
                <div class="name">GitHub</div>
                <div class="code-name">&amp;#xea0a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe694;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe694;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ca;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">防护盾</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b1;</span>
                <div class="name">暴力破解</div>
                <div class="code-name">&amp;#xe6b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">正版商用</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">正版验证</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b5;</span>
                <div class="name">计时收费</div>
                <div class="code-name">&amp;#xe6b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe779;</span>
                <div class="name">1086585</div>
                <div class="code-name">&amp;#xe779;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">首页-收费</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">安装</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f3;</span>
                <div class="name">防破解</div>
                <div class="code-name">&amp;#xe6f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b7;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xebcc;</span>
                <div class="name">安装</div>
                <div class="code-name">&amp;#xebcc;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1749285833238') format('woff2'),
       url('iconfont.woff?t=1749285833238') format('woff'),
       url('iconfont.ttf?t=1749285833238') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-youxiyouxiji"></span>
            <div class="name">
              游戏，游戏机
            </div>
            <div class="code-name">.icon-youxiyouxiji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiapianxingshi"></span>
            <div class="name">
              卡片形式
            </div>
            <div class="code-name">.icon-qiapianxingshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chengxuprogram1"></span>
            <div class="name">
              程序_program1
            </div>
            <div class="code-name">.icon-chengxuprogram1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-iconfontyouxihudong"></span>
            <div class="name">
              游戏
            </div>
            <div class="code-name">.icon-iconfontyouxihudong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao1"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youxi"></span>
            <div class="name">
              游戏
            </div>
            <div class="code-name">.icon-youxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao2"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjia"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-tianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao3"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuan"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-ziyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-com-kuaijiefangshi"></span>
            <div class="name">
              #com-快捷方式
            </div>
            <div class="code-name">.icon-com-kuaijiefangshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heikegongji"></span>
            <div class="name">
              黑客攻击
            </div>
            <div class="code-name">.icon-heikegongji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidonghua"></span>
            <div class="name">
              自动化
            </div>
            <div class="code-name">.icon-zidonghua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hezichengxu"></span>
            <div class="name">
              盒子程序
            </div>
            <div class="code-name">.icon-hezichengxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidonghua1"></span>
            <div class="name">
              自动化
            </div>
            <div class="code-name">.icon-zidonghua1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjia1"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-tianjia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao4"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiapian"></span>
            <div class="name">
              卡片
            </div>
            <div class="code-name">.icon-qiapian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kuaijiefangshi"></span>
            <div class="name">
              快捷方式
            </div>
            <div class="code-name">.icon-kuaijiefangshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heike"></span>
            <div class="name">
              黑客
            </div>
            <div class="code-name">.icon-heike
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kuaijiefangshi1"></span>
            <div class="name">
              快捷方式
            </div>
            <div class="code-name">.icon-kuaijiefangshi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-liebiao5"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.icon-liebiao5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin1"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin-"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiazengjiajia"></span>
            <div class="name">
              添加  增加   加
            </div>
            <div class="code-name">.icon-tianjiazengjiajia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kapianxingshi"></span>
            <div class="name">
              卡片形式
            </div>
            <div class="code-name">.icon-kapianxingshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kuaijiefangshi2"></span>
            <div class="name">
              快捷方式
            </div>
            <div class="code-name">.icon-kuaijiefangshi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heike1"></span>
            <div class="name">
              黑客
            </div>
            <div class="code-name">.icon-heike1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chengxu"></span>
            <div class="name">
              程序
            </div>
            <div class="code-name">.icon-chengxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jingjianban"></span>
            <div class="name">
              精简版
            </div>
            <div class="code-name">.icon-jingjianban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-rongqi267"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-a-rongqi267
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidonghua2"></span>
            <div class="name">
              自动化
            </div>
            <div class="code-name">.icon-zidonghua2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jingjianneirong"></span>
            <div class="name">
              精简内容
            </div>
            <div class="code-name">.icon-jingjianneirong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuye"></span>
            <div class="name">
              主页
            </div>
            <div class="code-name">.icon-zhuye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shipin"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.icon-shipin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianfang"></span>
            <div class="name">
              lianfang
            </div>
            <div class="code-name">.icon-lianfang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-GitHub"></span>
            <div class="name">
              GitHub
            </div>
            <div class="code-name">.icon-GitHub
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shipin1"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.icon-shipin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fanghudun"></span>
            <div class="name">
              防护盾
            </div>
            <div class="code-name">.icon-fanghudun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baolipojie"></span>
            <div class="name">
              暴力破解
            </div>
            <div class="code-name">.icon-baolipojie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhengbanshangyong"></span>
            <div class="name">
              正版商用
            </div>
            <div class="code-name">.icon-zhengbanshangyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhengbanyanzheng"></span>
            <div class="name">
              正版验证
            </div>
            <div class="code-name">.icon-zhengbanyanzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-timeCharge"></span>
            <div class="name">
              计时收费
            </div>
            <div class="code-name">.icon-timeCharge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-1086585"></span>
            <div class="name">
              1086585
            </div>
            <div class="code-name">.icon-1086585
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoufei"></span>
            <div class="name">
              首页-收费
            </div>
            <div class="code-name">.icon-shoufei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anzhuang"></span>
            <div class="name">
              安装
            </div>
            <div class="code-name">.icon-anzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shipin2"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.icon-shipin2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai1"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon-mianxing_fuzhi_xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-icon-mianxing_fuzhi_xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fangpojie"></span>
            <div class="name">
              防破解
            </div>
            <div class="code-name">.icon-fangpojie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anzhuang1"></span>
            <div class="name">
              安装
            </div>
            <div class="code-name">.icon-anzhuang1
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youxiyouxiji"></use>
                </svg>
                <div class="name">游戏，游戏机</div>
                <div class="code-name">#icon-youxiyouxiji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiapianxingshi"></use>
                </svg>
                <div class="name">卡片形式</div>
                <div class="code-name">#icon-qiapianxingshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chengxuprogram1"></use>
                </svg>
                <div class="name">程序_program1</div>
                <div class="code-name">#icon-chengxuprogram1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconfontyouxihudong"></use>
                </svg>
                <div class="name">游戏</div>
                <div class="code-name">#icon-iconfontyouxihudong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao1"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youxi"></use>
                </svg>
                <div class="name">游戏</div>
                <div class="code-name">#icon-youxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao2"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjia"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-tianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao3"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuan"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-ziyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-com-kuaijiefangshi"></use>
                </svg>
                <div class="name">#com-快捷方式</div>
                <div class="code-name">#icon-com-kuaijiefangshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heikegongji"></use>
                </svg>
                <div class="name">黑客攻击</div>
                <div class="code-name">#icon-heikegongji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidonghua"></use>
                </svg>
                <div class="name">自动化</div>
                <div class="code-name">#icon-zidonghua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hezichengxu"></use>
                </svg>
                <div class="name">盒子程序</div>
                <div class="code-name">#icon-hezichengxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidonghua1"></use>
                </svg>
                <div class="name">自动化</div>
                <div class="code-name">#icon-zidonghua1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjia1"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-tianjia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao4"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiapian"></use>
                </svg>
                <div class="name">卡片</div>
                <div class="code-name">#icon-qiapian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kuaijiefangshi"></use>
                </svg>
                <div class="name">快捷方式</div>
                <div class="code-name">#icon-kuaijiefangshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heike"></use>
                </svg>
                <div class="name">黑客</div>
                <div class="code-name">#icon-heike</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kuaijiefangshi1"></use>
                </svg>
                <div class="name">快捷方式</div>
                <div class="code-name">#icon-kuaijiefangshi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-liebiao5"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#icon-liebiao5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin1"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin-"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiazengjiajia"></use>
                </svg>
                <div class="name">添加  增加   加</div>
                <div class="code-name">#icon-tianjiazengjiajia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kapianxingshi"></use>
                </svg>
                <div class="name">卡片形式</div>
                <div class="code-name">#icon-kapianxingshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kuaijiefangshi2"></use>
                </svg>
                <div class="name">快捷方式</div>
                <div class="code-name">#icon-kuaijiefangshi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heike1"></use>
                </svg>
                <div class="name">黑客</div>
                <div class="code-name">#icon-heike1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chengxu"></use>
                </svg>
                <div class="name">程序</div>
                <div class="code-name">#icon-chengxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jingjianban"></use>
                </svg>
                <div class="name">精简版</div>
                <div class="code-name">#icon-jingjianban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-rongqi267"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-a-rongqi267</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidonghua2"></use>
                </svg>
                <div class="name">自动化</div>
                <div class="code-name">#icon-zidonghua2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jingjianneirong"></use>
                </svg>
                <div class="name">精简内容</div>
                <div class="code-name">#icon-jingjianneirong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuye"></use>
                </svg>
                <div class="name">主页</div>
                <div class="code-name">#icon-zhuye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shipin"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#icon-shipin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianfang"></use>
                </svg>
                <div class="name">lianfang</div>
                <div class="code-name">#icon-lianfang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-GitHub"></use>
                </svg>
                <div class="name">GitHub</div>
                <div class="code-name">#icon-GitHub</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shipin1"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#icon-shipin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanghudun"></use>
                </svg>
                <div class="name">防护盾</div>
                <div class="code-name">#icon-fanghudun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baolipojie"></use>
                </svg>
                <div class="name">暴力破解</div>
                <div class="code-name">#icon-baolipojie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhengbanshangyong"></use>
                </svg>
                <div class="name">正版商用</div>
                <div class="code-name">#icon-zhengbanshangyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhengbanyanzheng"></use>
                </svg>
                <div class="name">正版验证</div>
                <div class="code-name">#icon-zhengbanyanzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-timeCharge"></use>
                </svg>
                <div class="name">计时收费</div>
                <div class="code-name">#icon-timeCharge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-1086585"></use>
                </svg>
                <div class="name">1086585</div>
                <div class="code-name">#icon-1086585</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoufei"></use>
                </svg>
                <div class="name">首页-收费</div>
                <div class="code-name">#icon-shoufei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anzhuang"></use>
                </svg>
                <div class="name">安装</div>
                <div class="code-name">#icon-anzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shipin2"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#icon-shipin2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai1"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon-mianxing_fuzhi_xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-icon-mianxing_fuzhi_xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fangpojie"></use>
                </svg>
                <div class="name">防破解</div>
                <div class="code-name">#icon-fangpojie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anzhuang1"></use>
                </svg>
                <div class="name">安装</div>
                <div class="code-name">#icon-anzhuang1</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
