<UserControl x:Class="LSSOFT.Views.Modules.SoftwareLocalizationView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:models="clr-namespace:LSSOFT.Models"
    xmlns:vm="clr-namespace:LSSOFT.ViewModels"
    x:DataType="vm:SoftwareLocalizationViewModel">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  标题区域  -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  左侧标题和描述  -->
            <StackPanel Grid.Column="0" VerticalAlignment="Center" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,10,0" VerticalAlignment="Center"
                    Text="📁" FontSize="24" />
                <TextBlock
                    VerticalAlignment="Center" Text="安装包本地化整理归类"
                    FontSize="24" FontWeight="Bold" />
                <!--  描述文字  -->
                <TextBlock
                    VerticalAlignment="Center"
                    Text=" ——软件本地安装包文件整理、分类、重命名、移动等功能。" FontSize="14"
                    Foreground="#718096" />
            </StackPanel>

            <!--  右侧操作按钮区域  -->
            <StackPanel Grid.Column="2"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Orientation="Horizontal" Spacing="5">
                <TextBox
                    Name="SearchBox" Width="150"
                    VerticalAlignment="Center" Watermark="搜索安装包..." />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="📂"
                    ToolTip.Tip="选择文件夹" />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="🔄"
                    ToolTip.Tip="刷新扫描"
                    Command="{Binding ScanAndLoadPackagesCommand}" />
                <Button
                    Width="35" Height="35"
                    Content="□" ToolTip.Tip="资源管理器视图"
                    Classes="view-toggle-button selected" />
                <Button
                    Width="35" Height="35"
                    Content="☰" ToolTip.Tip="列表视图"
                    Classes="view-toggle-button" />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="⋯"
                    ToolTip.Tip="更多操作" />
            </StackPanel>
        </Grid>

        <!--  状态栏和统计信息  -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  左侧路径选择  -->
            <StackPanel Grid.Column="0"
                VerticalAlignment="Center"
                Orientation="Horizontal" Spacing="8">
                <TextBlock
                    VerticalAlignment="Center" Text="默认整理路径："
                    FontSize="12" Foreground="#6B7280" />
                <TextBox
                    Name="PathTextBox" Width="260"
                    VerticalAlignment="Center" FontSize="12"
                    Foreground="#374151" FontWeight="Medium"
                    Text="{Binding ScanPath, Mode=TwoWay}"
                    Watermark="输入路径或磁盘符(如: C, D, E)后按回车"
                    ToolTip.Tip="支持快速输入磁盘根目录：输入C按回车=C:\，或使用选择路径按钮" />
                <Button
                    Name="SelectPathButton" Width="70"
                    Height="30" Margin="0,0,0,0"
                    VerticalAlignment="Center" Content="选择路径"
                    FontSize="12" />
                <TextBlock
                    Margin="10,0,0,0" VerticalAlignment="Center"
                    Text="{Binding ScanStatus}" FontSize="12"
                    Foreground="#6B7280" />
            </StackPanel>
            <!--  批量操作按钮组 - 固定在右侧  -->
            <StackPanel Grid.Column="1"
                HorizontalAlignment="Right"
                VerticalAlignment="Top" Orientation="Horizontal"
                Spacing="5">
                <Button Content="📁 +分类" ToolTip.Tip="创建新的分类文件夹" Classes="add-category-button" Command="{Binding ShowAddCategoryDialogCommand}" />
                <Button Content="🏷️ 重命名" ToolTip.Tip="可批量重命名选中的安装包" Classes="batch-operation-button" />
                <Button Content="📦 移动" ToolTip.Tip="可批量移动选中的安装包到指定分类" Classes="batch-operation-button" />
                <Button Content="🗑️ 删除" ToolTip.Tip="可批量删除选中的安装包" Classes="batch-operation-button danger" />
            </StackPanel>
        </Grid>

        <!--  分类标签和操作区域  -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <!--  分类按钮列表 - 支持多行显示  -->
            <ItemsControl Grid.Column="0" ItemsSource="{Binding Categories}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="models:SoftwareCategoryInfo">
                        <Button Margin="0,0,5,5"
                                Classes="category-button"
                                IsEnabled="True"
                                Content="{Binding NameAndCount}"
                                Command="{Binding $parent[UserControl].DataContext.FilterByCategoryCommand}"
                                CommandParameter="{Binding Name}"
                                Classes.selected="{Binding IsSelected}" />
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Grid>

        <!--  安装包文件展示区域  -->
        <ScrollViewer Grid.Row="3"
            Name="PackageScrollViewer" Margin="0,0,0,20"
            VerticalScrollBarVisibility="Auto"
            HorizontalScrollBarVisibility="Disabled">
            <ItemsControl ItemsSource="{Binding LocalPackages}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Padding="10" Margin="0,0,0,8" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1">
                            <StackPanel Orientation="Horizontal" Spacing="16">
                                <Image Source="avares://LSSOFT/Assets/avalonia-logo.ico" Width="32" Height="32" Margin="0,0,10,0" />
                                <StackPanel Width="300">
                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14" />
                                    <TextBlock Text="{Binding FilePath}" FontSize="11" Foreground="#6B7280" />
                                </StackPanel>
                                <TextBlock Text="{Binding Size}" FontSize="12" Foreground="#059669" Margin="20,0,0,0" VerticalAlignment="Center" />
                                <TextBlock Text="{Binding CreatedDate, StringFormat='yyyy-MM-dd'}" FontSize="12" Foreground="#4B5563" Margin="20,0,0,0" VerticalAlignment="Center" />
                                <TextBlock Text="{Binding Version, TargetNullValue=''}" FontSize="12" Foreground="#4B5563" Margin="20,0,0,0" VerticalAlignment="Center" />
                                <TextBlock Text="未分类" FontSize="12" Foreground="#A0AEC0" Margin="20,0,0,0" VerticalAlignment="Center" />
                                <Button Content="详情" CommandParameter="{Binding}" Click="OnShowDetailClick" />
                            </StackPanel>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>

    <!--  样式定义  -->
    <UserControl.Styles>
        <!--  分类按钮样式  -->
        <Style Selector="Button.category-button">
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="Margin" Value="0,0,5,0" />
            <Setter Property="CornerRadius" Value="15" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  选中状态样式  -->
        <Style Selector="Button.category-button.selected">
            <Setter Property="Background" Value="#3182CE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#3182CE" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <Style Selector="Button.category-button.selected:pointerover">
            <Setter Property="Background" Value="#2C5AA0" />
            <Setter Property="BorderBrush" Value="#2C5AA0" />
        </Style>

        <!--  添加分类按钮样式  -->
        <Style Selector="Button.add-category-button">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.add-category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.add-category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  批量操作按钮样式  -->
        <Style Selector="Button.batch-operation-button">
            <Setter Property="Padding" Value="10,6" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#D1D5DB" />
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="Foreground" Value="#374151" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.batch-operation-button:pointerover">
            <Setter Property="Background" Value="#F3F4F6" />
            <Setter Property="BorderBrush" Value="#9CA3AF" />
        </Style>

        <Style Selector="Button.batch-operation-button.danger">
            <Setter Property="Background" Value="#FEF2F2" />
            <Setter Property="BorderBrush" Value="#FECACA" />
            <Setter Property="Foreground" Value="#DC2626" />
        </Style>

        <Style Selector="Button.batch-operation-button.danger:pointerover">
            <Setter Property="Background" Value="#FEE2E2" />
            <Setter Property="BorderBrush" Value="#FCA5A5" />
        </Style>

        <!--  视图切换按钮样式  -->
        <Style Selector="Button.view-toggle-button">
            <Setter Property="Padding" Value="8" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#D1D5DB" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#6B7280" />
        </Style>

        <Style Selector="Button.view-toggle-button:pointerover">
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="BorderBrush" Value="#9CA3AF" />
        </Style>

        <Style Selector="Button.view-toggle-button.selected">
            <Setter Property="Background" Value="#3B82F6" />
            <Setter Property="BorderBrush" Value="#3B82F6" />
            <Setter Property="Foreground" Value="White" />
        </Style>

        <!--  列表操作按钮样式  -->
        <Style Selector="Button.list-action-button">
            <Setter Property="Padding" Value="3" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="Foreground" Value="#6B7280" />
            <Setter Property="FontSize" Value="10" />
        </Style>

        <Style Selector="Button.list-action-button:pointerover">
            <Setter Property="Background" Value="#F3F4F6" />
            <Setter Property="Foreground" Value="#374151" />
        </Style>

        <Style Selector="Button.list-action-button.danger">
            <Setter Property="Background" Value="#FEE2E2" />
            <Setter Property="Foreground" Value="#DC2626" />
        </Style>

        <Style Selector="Button.list-action-button.danger:pointerover">
            <Setter Property="Background" Value="#FECACA" />
        </Style>
    </UserControl.Styles>

</UserControl>




