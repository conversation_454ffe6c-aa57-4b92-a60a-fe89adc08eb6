<UserControl
    x:Class="LSSOFT.Views.Modules.SoftwareLocalizationView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:models="clr-namespace:LSSOFT.Models"
    xmlns:vm="clr-namespace:LSSOFT.ViewModels"
    xmlns:converters="clr-namespace:LSSOFT.Views.Modules"
    xmlns:conv="clr-namespace:LSSOFT.Converters"
    xmlns:system="clr-namespace:System;assembly=System.Runtime"
    x:DataType="vm:SoftwareLocalizationViewModel">

    <UserControl.Resources>
        <converters:PathToBitmapConverter x:Key="PathToBitmapConverter" />
        <conv:CategorySoftwareParameterConverter x:Key="CategorySoftwareParameterConverter" />
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题区域  -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  左侧标题和描述  -->
            <StackPanel Grid.Column="0" VerticalAlignment="Center" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,10,0" VerticalAlignment="Center"
                    Text="📁" FontSize="24" />
                <TextBlock
                    VerticalAlignment="Center" Text="安装包本地化整理归类"
                    FontSize="24" FontWeight="Bold" />
                <!--  描述文字  -->
                <TextBlock
                    VerticalAlignment="Center"
                    Text=" ——软件本地安装包文件整理、分类、重命名、移动等功能。" FontSize="14"
                    Foreground="#718096" />
            </StackPanel>

            <!--  右侧操作按钮区域  -->
            <StackPanel Grid.Column="2"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Orientation="Horizontal" Spacing="5">
                <TextBox
                    Name="SearchBox" Width="150"
                    VerticalAlignment="Center" Watermark="搜索安装包..." />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="📂"
                    ToolTip.Tip="选择文件夹" />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="🔄"
                    ToolTip.Tip="刷新扫描"
                    Command="{Binding ScanAndLoadPackagesCommand}" />
                <Button
                    Width="35" Height="35"
                    Content="□" ToolTip.Tip="卡片视图"
                    Command="{Binding SwitchToCardViewCommand}"
                    Classes="view-toggle-button"
                    Classes.selected="{Binding IsCardView}" />
                <Button
                    Width="35" Height="35"
                    Content="☰" ToolTip.Tip="列表视图"
                    Command="{Binding SwitchToListViewCommand}"
                    Classes="view-toggle-button"
                    Classes.selected="{Binding IsListView}" />
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" Content="⋯"
                    ToolTip.Tip="更多操作" />
            </StackPanel>
        </Grid>

        <!--  状态栏和统计信息  -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  左侧路径选择  -->
            <StackPanel Grid.Column="0"
                VerticalAlignment="Center"
                Orientation="Horizontal" Spacing="8">
                <TextBlock
                    VerticalAlignment="Center" Text="默认整理路径："
                    FontSize="12" Foreground="#6B7280" />
                <TextBox
                    Name="PathTextBox" Width="260"
                    VerticalAlignment="Center" FontSize="12"
                    Foreground="#374151" FontWeight="Medium"
                    Text="{Binding ScanPath, Mode=TwoWay}"
                    Watermark="输入路径或磁盘符(如: C, D, E)后按回车"
                    ToolTip.Tip="支持快速输入磁盘根目录：输入C按回车=C:\，或使用选择路径按钮" />
                <Button
                    Name="SelectPathButton" Width="70"
                    Height="30" Margin="0,0,0,0"
                    VerticalAlignment="Center" Content="选择路径"
                    FontSize="12" />
                <TextBlock
                    Margin="10,0,0,0" VerticalAlignment="Center"
                    Text="{Binding ScanStatus}"
                    FontSize="12" Foreground="#6B7280" />
            </StackPanel>
            <!--  批量操作按钮组 - 固定在右侧  -->
            <StackPanel Grid.Column="1"
                HorizontalAlignment="Right"
                VerticalAlignment="Top" Orientation="Horizontal"
                Spacing="5">
                <Button
                    Content="📁 +分类" ToolTip.Tip="创建新的分类文件夹"
                    Classes="add-category-button"
                    Command="{Binding ShowAddCategoryDialogCommand}" />
                <Button Content="🏷️ 重命名" ToolTip.Tip="可批量重命名选中的安装包" Classes="batch-operation-button" />
                <Button Content="📦 移动" ToolTip.Tip="可批量移动选中的安装包到指定分类" Classes="batch-operation-button" />
                <Button Content="🗑️ 删除" ToolTip.Tip="可批量删除选中的安装包" Classes="batch-operation-button danger" />
            </StackPanel>
        </Grid>

        <!--  分类标签和操作区域  -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <!--  分类按钮列表 - 支持多行显示  -->
            <ItemsControl Grid.Column="0" ItemsSource="{Binding Categories}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="models:SoftwareCategoryInfo">
                        <Border
                            Margin="0,0,5,5" Padding="12,6"
                            Classes="category-button" CornerRadius="15"
                            Cursor="Hand" Tapped="OnCategoryTapped"
                            PointerPressed="OnCategoryPointerPressed"
                            PointerMoved="OnCategoryPointerMoved"
                            PointerReleased="OnCategoryPointerReleased">
                            <Border.Background>
                                <SolidColorBrush Color="{Binding IsSelected, Converter={StaticResource BoolToColorConverter}}" />
                            </Border.Background>
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Text="{Binding NameAndCount}"
                                FontSize="12" FontWeight="Medium"
                                Foreground="Black" />
                            <Border.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="修改分类" Command="{Binding $parent[UserControl].DataContext.ShowEditCategoryDialogCommand}" CommandParameter="{Binding Name}">
                                        <MenuItem.Icon>
                                            <TextBlock Text="✏️" />
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="删除分类" Command="{Binding $parent[UserControl].DataContext.DeleteCategoryCommand}" CommandParameter="{Binding Name}">
                                        <MenuItem.Icon>
                                            <TextBlock Text="🗑️" />
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </Border.ContextMenu>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Grid>

        <!--  安装包文件展示区域  -->
        <ScrollViewer Grid.Row="3"
            Name="PackageScrollViewer" Margin="0,0,0,10"
            VerticalScrollBarVisibility="Auto"
            HorizontalScrollBarVisibility="Disabled">
            <StackPanel HorizontalAlignment="Stretch">

                <!--  卡片模式 ItemsControl  -->
                <ItemsControl Name="SoftwareCardItemsControl" ItemsSource="{Binding LocalPackages}" IsVisible="{Binding IsCardView}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border
                                Width="270" Height="120"
                                Margin="5" CornerRadius="8"
                                Background="White" BorderBrush="#E5E7EB"
                                BorderThickness="1"
                                DragDrop.AllowDrop="True"
                                IsVisible="{Binding IsVisible}">
                                <Grid Margin="12,8,12,6">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <!--  第一行：图标和软件名称  -->
                                    <Grid Grid.Row="0" Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <!--  左侧图标  -->
                                        <Border Grid.Column="0"
                                            Width="48" Height="48"
                                            Margin="0,0,5,0" CornerRadius="6"
                                            Background="#F3F4F6">
                                            <Image
                                                Width="32" Height="32"
                                                Source="{Binding IconPath, Converter={StaticResource PathToBitmapConverter}}" />
                                        </Border>

                                        <!--  右侧软件名称和路径  -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Top">
                                            <TextBlock
                                                Margin="0,0,0,2"
                                                Text="{Binding DisplayName}"
                                                FontSize="13" FontWeight="Bold"
                                                Foreground="#1F2937"
                                                TextTrimming="CharacterEllipsis"
                                                MaxLines="1" />
                                            <TextBlock
                                                MaxHeight="24"
                                                Text="{Binding FilePath}"
                                                FontSize="10" Foreground="#6B7280"
                                                TextTrimming="CharacterEllipsis"
                                                MaxLines="2" />
                                        </StackPanel>
                                    </Grid>

                                    <!--  第二行：文件信息  -->
                                    <Grid Grid.Row="1" Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="auto" />
                                            <ColumnDefinition Width="auto" />                                            
                                            <ColumnDefinition Width="auto" />
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="2" Margin="0,0,3,0">
                                            <TextBlock Text="大小:" FontSize="10" Foreground="#6B7280" />
                                            <TextBlock
                                                Text="{Binding Size}"
                                                FontSize="10" Foreground="#059669"
                                                FontWeight="SemiBold"
                                                TextTrimming="CharacterEllipsis"
                                                MaxWidth="50" />
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="2" Margin="0,0,3,0">
                                            <TextBlock Text="日期:" FontSize="10" Foreground="#6B7280" />
                                            <TextBlock
                                                Text="{Binding CreatedDate, StringFormat='MM-dd'}"
                                                FontSize="10" Foreground="#4B5563"
                                                FontWeight="SemiBold" />
                                        </StackPanel>

                                        <!--  版本信息  -->
                                        <Border Grid.Column="2" Margin="0,0,0,0"
                                            Padding="4,2" Background="#E0F2FE" CornerRadius="3"
                                            HorizontalAlignment="Left"
                                            IsVisible="{Binding Version, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                            <StackPanel Orientation="Horizontal" Spacing="1">
                                                <TextBlock
                                                    Text="版本:" FontSize="8"
                                                    Foreground="#0369A1" FontWeight="Medium" />
                                                <TextBlock
                                                    Text="{Binding Version}"
                                                    FontSize="8" Foreground="#0369A1"
                                                    FontWeight="Bold"
                                                    TextTrimming="CharacterEllipsis"
                                                    MaxWidth="40" />
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <!--  第三行：分类标签  -->
                                    <ItemsControl Grid.Row="2" ItemsSource="{Binding Categories}" Margin="0,0,0,8">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <WrapPanel Orientation="Horizontal" />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border
                                                    Name="CategoryBorder" Margin="0,0,4,2"
                                                    Padding="6,3"
                                                    Background="{Binding ., Converter={StaticResource CategoryToColorConverter}}"
                                                    CornerRadius="10"
                                                    PointerPressed="OnCategoryBorderPointerPressed">
                                                    <Border.ContextMenu>
                                                        <ContextMenu>
                                                            <MenuItem Header="删除此分类" Click="OnRemoveCategoryFromSoftwareFinal" Tag="{Binding .}">
                                                                <MenuItem.Icon>
                                                                    <TextBlock Text="🗑️" FontSize="10" />
                                                                </MenuItem.Icon>
                                                            </MenuItem>
                                                        </ContextMenu>
                                                    </Border.ContextMenu>
                                                    <TextBlock
                                                        Text="{Binding .}"
                                                        FontSize="9" Foreground="White"
                                                        FontWeight="Medium" />
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!--  列表模式 ItemsControl  -->
                <ItemsControl Name="SoftwareListItemsControl" ItemsSource="{Binding LocalPackages}" IsVisible="{Binding IsListView}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border
                                Margin="0,0,0,8" Padding="10"
                                Background="White" BorderBrush="#E5E7EB"
                                BorderThickness="0,0,0,1"
                                DragDrop.AllowDrop="True"
                                IsVisible="{Binding IsVisible}">
                                <StackPanel Orientation="Horizontal" Spacing="16">
                                    <Image
                                        Width="32" Height="32"
                                        Margin="0,0,10,0"
                                        Source="{Binding IconPath, Converter={StaticResource PathToBitmapConverter}}" />
                                    <StackPanel Width="300">
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="14" />
                                        <TextBlock Text="{Binding FilePath}" FontSize="11" Foreground="#6B7280" />
                                    </StackPanel>
                                    <TextBlock
                                        Width="100" Margin="20,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding Size}"
                                        FontSize="12" Foreground="#059669" />
                                    <TextBlock
                                        Width="100" Margin="20,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding CreatedDate, StringFormat='yyyy-MM-dd'}"
                                        FontSize="12" Foreground="#4B5563" />

                                    <!--  版本信息显示区域（固定占位）  -->
                                    <Border Width="100" Margin="20,0,0,0" VerticalAlignment="Center">
                                        <Border
                                            Padding="8,4" Background="#E0F2FE" CornerRadius="8"
                                            IsVisible="{Binding Version, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                            <StackPanel Orientation="Horizontal" Spacing="4">
                                                <TextBlock
                                                    Text="版本:" FontSize="10"
                                                    Foreground="#0369A1" FontWeight="Medium" />
                                                <TextBlock
                                                    Text="{Binding Version}"
                                                    FontSize="11" Foreground="#0369A1"
                                                    FontWeight="Bold" />
                                            </StackPanel>
                                        </Border>
                                    </Border>

                                    <!--  分类标签区域  -->
                                    <StackPanel Margin="20,0,0,0" VerticalAlignment="Center" Orientation="Horizontal">
                                        <ItemsControl ItemsSource="{Binding Categories}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal" Spacing="5" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border
                                                        Name="CategoryBorder" Margin="0,0,5,0"
                                                        Padding="8,4"
                                                        Background="{Binding ., Converter={StaticResource CategoryToColorConverter}}"
                                                        CornerRadius="12"
                                                        PointerPressed="OnCategoryBorderPointerPressed">
                                                        <Border.ContextMenu>
                                                            <ContextMenu>
                                                                <MenuItem Header="删除此分类" Click="OnRemoveCategoryFromSoftwareFinal" Tag="{Binding .}">
                                                                    <MenuItem.Icon>
                                                                        <TextBlock Text="🗑️" FontSize="12" />
                                                                    </MenuItem.Icon>
                                                                </MenuItem>
                                                            </ContextMenu>
                                                        </Border.ContextMenu>
                                                        <TextBlock
                                                            Text="{Binding .}"
                                                            FontSize="11" Foreground="White"
                                                            FontWeight="Medium" />
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!--  底部调试状态栏  -->
        <Border Grid.Row="4"
            Padding="10,5" Background="#F8F9FA"
            BorderBrush="#E9ECEF" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  调试信息标签  -->
                <TextBlock Grid.Column="0"
                    Margin="0,0,10,0" VerticalAlignment="Center"
                    Text="🔧 调试信息:" FontSize="11"
                    FontWeight="Medium" Foreground="#495057" />

                <!--  调试信息内容  -->
                <TextBlock Grid.Column="1"
                    Name="DebugStatusText" VerticalAlignment="Center"
                    Text="{Binding DebugMessage}"
                    FontSize="11" Foreground="#6C757D"
                    TextWrapping="NoWrap"
                    TextTrimming="CharacterEllipsis" />

                <!--  清除按钮  -->
                <Button Grid.Column="2"
                    Name="ClearDebugButton" Margin="10,0,0,0"
                    Padding="8,2" Content="清除"
                    FontSize="10" Classes="debug-clear-button" />
            </Grid>
        </Border>

        <!--  添加分类对话框  -->
        <Border Grid.RowSpan="5" Background="#80000000" IsVisible="{Binding IsAddCategoryDialogVisible}">
            <Border
                MaxWidth="400" Padding="20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" Background="White"
                CornerRadius="8" BorderBrush="#E2E8F0"
                BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock
                        HorizontalAlignment="Center" Text="添加软件分类"
                        FontSize="18" FontWeight="SemiBold" />

                    <TextBox Width="300" Watermark="请输入分类名称..." Text="{Binding NewCategoryName}" />

                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal" Spacing="10">
                        <Button
                            Width="80" Content="确定"
                            Command="{Binding AddCategoryCommand}"
                            Classes="primary-button" />
                        <Button
                            Width="80" Content="取消"
                            Command="{Binding CancelAddCategoryCommand}"
                            Classes="secondary-button" />
                    </StackPanel>
                </StackPanel>
            </Border>
        </Border>

        <!--  修改分类对话框  -->
        <Border Grid.RowSpan="5" Background="#80000000" IsVisible="{Binding IsEditCategoryDialogVisible}">
            <Border
                MaxWidth="400" Padding="20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" Background="White"
                CornerRadius="8" BorderBrush="#E2E8F0"
                BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock
                        HorizontalAlignment="Center" Text="修改软件分类"
                        FontSize="18" FontWeight="SemiBold" />

                    <TextBox Width="300" Watermark="请输入新的分类名称..." Text="{Binding EditCategoryName}" />

                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal" Spacing="10">
                        <Button
                            Width="80" Content="确定"
                            Command="{Binding EditCategoryCommand}"
                            Classes="primary-button" />
                        <Button
                            Width="80" Content="取消"
                            Command="{Binding CancelEditCategoryCommand}"
                            Classes="secondary-button" />
                    </StackPanel>
                </StackPanel>
            </Border>
        </Border>
    </Grid>

    <!--  样式定义  -->
    <UserControl.Styles>
        <!--  分类按钮样式  -->
        <Style Selector="Button.category-button">
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="Margin" Value="0,0,5,0" />
            <Setter Property="CornerRadius" Value="15" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  选中状态样式  -->
        <Style Selector="Button.category-button.selected">
            <Setter Property="Background" Value="#3182CE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#3182CE" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <Style Selector="Button.category-button.selected:pointerover">
            <Setter Property="Background" Value="#2C5AA0" />
            <Setter Property="BorderBrush" Value="#2C5AA0" />
        </Style>

        <!--  添加分类按钮样式  -->
        <Style Selector="Button.add-category-button">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.add-category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.add-category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  批量操作按钮样式  -->
        <Style Selector="Button.batch-operation-button">
            <Setter Property="Padding" Value="10,6" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#D1D5DB" />
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="Foreground" Value="#374151" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.batch-operation-button:pointerover">
            <Setter Property="Background" Value="#F3F4F6" />
            <Setter Property="BorderBrush" Value="#9CA3AF" />
        </Style>

        <Style Selector="Button.batch-operation-button.danger">
            <Setter Property="Background" Value="#FEF2F2" />
            <Setter Property="BorderBrush" Value="#FECACA" />
            <Setter Property="Foreground" Value="#DC2626" />
        </Style>

        <Style Selector="Button.batch-operation-button.danger:pointerover">
            <Setter Property="Background" Value="#FEE2E2" />
            <Setter Property="BorderBrush" Value="#FCA5A5" />
        </Style>

        <!--  视图切换按钮样式  -->
        <Style Selector="Button.view-toggle-button">
            <Setter Property="Padding" Value="8" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#D1D5DB" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#6B7280" />
        </Style>

        <Style Selector="Button.view-toggle-button:pointerover">
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="BorderBrush" Value="#9CA3AF" />
        </Style>

        <Style Selector="Button.view-toggle-button.selected">
            <Setter Property="Background" Value="#3B82F6" />
            <Setter Property="BorderBrush" Value="#3B82F6" />
            <Setter Property="Foreground" Value="White" />
        </Style>

        <!--  列表操作按钮样式  -->
        <Style Selector="Button.list-action-button">
            <Setter Property="Padding" Value="3" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="Foreground" Value="#6B7280" />
            <Setter Property="FontSize" Value="10" />
        </Style>

        <Style Selector="Button.list-action-button:pointerover">
            <Setter Property="Background" Value="#F3F4F6" />
            <Setter Property="Foreground" Value="#374151" />
        </Style>

        <Style Selector="Button.list-action-button.danger">
            <Setter Property="Background" Value="#FEE2E2" />
            <Setter Property="Foreground" Value="#DC2626" />
        </Style>

        <Style Selector="Button.list-action-button.danger:pointerover">
            <Setter Property="Background" Value="#FECACA" />
        </Style>

        <!--  调试清除按钮样式  -->
        <Style Selector="Button.debug-clear-button">
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#DEE2E6" />
            <Setter Property="Background" Value="#F8F9FA" />
            <Setter Property="Foreground" Value="#6C757D" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.debug-clear-button:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
            <Setter Property="BorderBrush" Value="#ADB5BD" />
            <Setter Property="Foreground" Value="#495057" />
        </Style>

        <Style Selector="Button.debug-clear-button:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <!--  对话框按钮样式  -->
        <Style Selector="Button.primary-button">
            <Setter Property="Background" Value="#3182CE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="16,8" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.primary-button:pointerover">
            <Setter Property="Background" Value="#2C5AA0" />
        </Style>

        <Style Selector="Button.primary-button:pressed">
            <Setter Property="Background" Value="#2A4A82" />
        </Style>

        <Style Selector="Button.secondary-button">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="16,8" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.secondary-button:pointerover">
            <Setter Property="Background" Value="#EDF2F7" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.secondary-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>
    </UserControl.Styles>

</UserControl>




