using CommunityToolkit.Mvvm.ComponentModel;

namespace LSSOFT.Models;

/// <summary>
/// 软件分类信息
/// </summary>
public partial class SoftwareCategoryInfo : ObservableObject
{
    /// <summary>
    /// 分类名称
    /// </summary>
    [ObservableProperty]
    private string _name = string.Empty;

    /// <summary>
    /// 是否选中
    /// </summary>
    [ObservableProperty]
    private bool _isSelected;

    /// <summary>
    /// 分类类型
    /// </summary>
    public SoftwareType? Type { get; set; }

    /// <summary>
    /// 是否为自定义分类
    /// </summary>
    public bool IsCustom { get; set; }

    /// <summary>
    /// 分类数量
    /// </summary>
    [ObservableProperty]
    private int _count;

    public SoftwareCategoryInfo()
    {
    }

    public SoftwareCategoryInfo(string name, SoftwareType? type = null, bool isCustom = false)
    {
        Name = name;
        Type = type;
        IsCustom = isCustom;
    }

    public string NameAndCount => $"{Name}({Count})";

    public override string ToString() => NameAndCount;
}
