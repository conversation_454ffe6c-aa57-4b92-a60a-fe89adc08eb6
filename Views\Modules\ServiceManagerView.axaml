<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="LSSOFT.Views.Modules.ServiceManagerView">

    <StackPanel Margin="20">
        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="⚙️" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="服务管理" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
        </StackPanel>
        
        <TextBlock Text="管理Windows系统服务。" 
                  FontSize="16" Foreground="#718096" Margin="0,0,0,20"/>
        
        <Border Background="#F7FAFC" CornerRadius="8" Padding="20">
            <TextBlock Text="功能开发中..." FontSize="14" HorizontalAlignment="Center"/>
        </Border>
    </StackPanel>

</UserControl>