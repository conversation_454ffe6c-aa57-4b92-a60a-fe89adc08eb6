using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LSSOFT.Models;
using Microsoft.Win32;
using System.IO;
using System.Linq;

namespace LSSOFT.Services;

/// <summary>
/// 软件管理服务
/// </summary>
public class SoftwareService
{
    private const string UninstallKey32 = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall";
    private const string UninstallKey64 = @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall";

    /// <summary>
    /// 获取已安装软件列表
    /// </summary>
    /// <returns>软件列表</returns>
    public async Task<List<SoftwareInfo>> GetInstalledSoftwareAsync()
    {
        var softwareList = new List<SoftwareInfo>();
        if (!OperatingSystem.IsWindows())
            return softwareList;

        return await Task.Run(() =>
        {
            // 获取32位软件
            GetSoftwareFromRegistry(UninstallKey32, softwareList);
            // 获取64位软件
            GetSoftwareFromRegistry(UninstallKey64, softwareList);
            return softwareList;
        });
    }

    private void GetSoftwareFromRegistry(string registryKey, List<SoftwareInfo> softwareList)
    {
        if (!OperatingSystem.IsWindows())
            return;
        try
        {
            using var key = Registry.LocalMachine.OpenSubKey(registryKey);
            if (key == null) return;

            foreach (var subKeyName in key.GetSubKeyNames())
            {
                using var subKey = key.OpenSubKey(subKeyName);
                if (subKey == null) continue;

                var displayName = subKey.GetValue("DisplayName")?.ToString();
                if (string.IsNullOrEmpty(displayName)) continue;

                var publisher = subKey.GetValue("Publisher")?.ToString() ?? string.Empty;
                var software = new SoftwareInfo
                {
                    Name = displayName,
                    Version = subKey.GetValue("DisplayVersion")?.ToString() ?? string.Empty,
                    Publisher = publisher,
                    InstallDate = GetInstallDate(subKey),
                    Size = GetSoftwareSize(subKey),
                    InstallPath = subKey.GetValue("InstallLocation")?.ToString() ?? string.Empty,
                    UninstallCommand = subKey.GetValue("UninstallString")?.ToString() ?? string.Empty,
                    Type = DetermineSoftwareType(displayName, publisher),
                    IsSystemSoftware = IsSystemSoftware(displayName),
                    Description = subKey.GetValue("DisplayComment")?.ToString() ?? string.Empty
                };

                softwareList.Add(software);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error reading registry key {registryKey}: {ex.Message}");
        }
    }

    private DateTime GetInstallDate(RegistryKey key)
    {
        if (!OperatingSystem.IsWindows())
            return DateTime.Now;
        var installDate = key.GetValue("InstallDate")?.ToString();
        if (string.IsNullOrEmpty(installDate)) return DateTime.Now;

        if (DateTime.TryParse(installDate, out var date))
            return date;

        return DateTime.Now;
    }

    private string GetSoftwareSize(RegistryKey key)
    {
        if (!OperatingSystem.IsWindows())
            return "未知";
        var size = key.GetValue("EstimatedSize")?.ToString();
        if (string.IsNullOrEmpty(size)) return "未知";

        if (long.TryParse(size, out var sizeInKB))
        {
            if (sizeInKB > 1024 * 1024)
                return $"{sizeInKB / (1024 * 1024):F1} GB";
            if (sizeInKB > 1024)
                return $"{sizeInKB / 1024:F1} MB";
            return $"{sizeInKB} KB";
        }

        return "未知";
    }

    private SoftwareType DetermineSoftwareType(string name, string publisher)
    {
        name = name.ToLower();
        publisher = publisher ?? string.Empty;
        publisher = publisher.ToLower();

        if (name.Contains("game") || name.Contains("steam") || name.Contains("epic") || 
            name.Contains("origin") || name.Contains("battle.net"))
            return SoftwareType.Game;

        if (name.Contains("visual studio") || name.Contains("vs code") || 
            name.Contains("intellij") || name.Contains("eclipse") || 
            name.Contains("android studio") || name.Contains("git"))
            return SoftwareType.Development;

        if (name.Contains("office") || name.Contains("word") || name.Contains("excel") || 
            name.Contains("powerpoint") || name.Contains("wps"))
            return SoftwareType.Office;

        if (name.Contains("autocad") || name.Contains("solidworks") || 
            name.Contains("catia") || name.Contains("revit"))
            return SoftwareType.Engineering;

        if (name.Contains("windows") || name.Contains("microsoft") || 
            name.Contains("intel") || name.Contains("amd") || 
            name.Contains("nvidia"))
            return SoftwareType.System;

        return SoftwareType.Other;
    }

    private bool IsSystemSoftware(string name)
    {
        name = name.ToLower();
        return name.Contains("windows") || 
               name.Contains("microsoft") || 
               name.Contains("intel") || 
               name.Contains("amd") || 
               name.Contains("nvidia") ||
               name.Contains("driver");
    }

    /// <summary>
    /// 搜索软件
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="type">软件类型</param>
    /// <returns>搜索结果</returns>
    public async Task<List<SoftwareInfo>> SearchSoftwareAsync(string keyword, SoftwareType? type = null)
    {
        var allSoftware = await GetInstalledSoftwareAsync();
        var result = new List<SoftwareInfo>();

        foreach (var software in allSoftware)
        {
            bool matchKeyword = string.IsNullOrEmpty(keyword) || 
                               software.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                               software.Publisher.Contains(keyword, StringComparison.OrdinalIgnoreCase);

            bool matchType = type == null || software.Type == type;

            if (matchKeyword && matchType)
            {
                result.Add(software);
            }
        }

        return result;
    }

    /// <summary>
    /// 卸载软件
    /// </summary>
    /// <param name="software">要卸载的软件</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UninstallSoftwareAsync(SoftwareInfo software)
    {
        // 实际实现应该调用系统卸载程序
        await Task.Delay(1000); // 模拟卸载过程
        return true;
    }

    /// <summary>
    /// 启动软件
    /// </summary>
    /// <param name="software">要启动的软件</param>
    /// <returns>是否成功</returns>
    public async Task<bool> LaunchSoftwareAsync(SoftwareInfo software)
    {
        try
        {
            // 实际实现应该启动软件
            await Task.Delay(100);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 打开软件安装位置
    /// </summary>
    /// <param name="software">软件信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> OpenInstallLocationAsync(SoftwareInfo software)
    {
        try
        {
            // 实际实现应该打开文件夹
            await Task.Delay(100);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
