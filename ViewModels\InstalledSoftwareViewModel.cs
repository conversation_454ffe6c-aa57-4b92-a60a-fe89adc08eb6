using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Models;
using LSSOFT.Services;

namespace LSSOFT.ViewModels;

/// <summary>
/// 已安装软件视图模型
/// </summary>
public partial class InstalledSoftwareViewModel : ViewModelBase
{
    private readonly SoftwareService _softwareService;

    // 移除[ObservableProperty]特性，手动声明属性
    private ObservableCollection<SoftwareInfo> _softwareList = new();
    public ObservableCollection<SoftwareInfo> SoftwareList
    {
        get => _softwareList;
        set => SetProperty(ref _softwareList, value);
    }

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private int _selectedSoftwareTypeIndex = 0;

    [ObservableProperty]
    private string _statusText = "就绪";

    [ObservableProperty]
    private int _totalSoftwareCount;

    [ObservableProperty]
    private string _totalSize = "0 MB";

    public InstalledSoftwareViewModel()
    {
        _softwareService = new SoftwareService();
        
        // 初始化时加载数据
        _ = LoadSoftwareListAsync();
    }

    /// <summary>
    /// 加载软件列表
    /// </summary>
    [RelayCommand]
    public async Task LoadSoftwareListAsync()
    {
        try
        {
            IsLoading = true;
            StatusText = "正在加载软件列表...";

            var softwareList = await _softwareService.GetInstalledSoftwareAsync();
            
            SoftwareList.Clear();
            foreach (var software in softwareList)
            {
                SoftwareList.Add(software);
            }

            TotalSoftwareCount = SoftwareList.Count;
            UpdateStatusText();
            StatusText = "就绪";
        }
        catch (Exception ex)
        {
            StatusText = $"加载失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 搜索软件
    /// </summary>
    [RelayCommand]
    private async Task SearchSoftwareAsync()
    {
        try
        {
            IsLoading = true;
            StatusText = "正在搜索...";

            var softwareType = SelectedSoftwareTypeIndex switch
            {
                1 => SoftwareType.System,
                2 => SoftwareType.Development,
                3 => SoftwareType.Game,
                4 => SoftwareType.Office,
                5 => SoftwareType.Engineering,
                _ => (SoftwareType?)null
            };

            var results = await _softwareService.SearchSoftwareAsync(SearchText, softwareType);
            
            SoftwareList.Clear();
            foreach (var software in results)
            {
                SoftwareList.Add(software);
            }

            TotalSoftwareCount = SoftwareList.Count;
            UpdateStatusText();
            StatusText = "搜索完成";
        }
        catch (Exception ex)
        {
            StatusText = $"搜索失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 刷新软件列表
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await LoadSoftwareListAsync();
    }

    /// <summary>
    /// 卸载软件
    /// </summary>
    [RelayCommand]
    private async Task UninstallSoftwareAsync(SoftwareInfo software)
    {
        if (software == null) return;

        try
        {
            StatusText = $"正在卸载 {software.Name}...";
            var success = await _softwareService.UninstallSoftwareAsync(software);
            
            if (success)
            {
                SoftwareList.Remove(software);
                TotalSoftwareCount = SoftwareList.Count;
                UpdateStatusText();
                StatusText = $"{software.Name} 卸载成功";
            }
            else
            {
                StatusText = $"{software.Name} 卸载失败";
            }
        }
        catch (Exception ex)
        {
            StatusText = $"卸载失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 启动软件
    /// </summary>
    [RelayCommand]
    private async Task LaunchSoftwareAsync(SoftwareInfo software)
    {
        if (software == null) return;

        try
        {
            StatusText = $"正在启动 {software.Name}...";
            var success = await _softwareService.LaunchSoftwareAsync(software);
            
            StatusText = success ? $"{software.Name} 启动成功" : $"{software.Name} 启动失败";
        }
        catch (Exception ex)
        {
            StatusText = $"启动失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 打开安装位置
    /// </summary>
    [RelayCommand]
    private async Task OpenInstallLocationAsync(SoftwareInfo software)
    {
        if (software == null) return;

        try
        {
            StatusText = $"正在打开 {software.Name} 安装位置...";
            var success = await _softwareService.OpenInstallLocationAsync(software);
            
            StatusText = success ? "安装位置已打开" : "打开安装位置失败";
        }
        catch (Exception ex)
        {
            StatusText = $"打开失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 批量卸载选中的软件
    /// </summary>
    [RelayCommand]
    private async Task BatchUninstallAsync()
    {
        var selectedSoftware = SoftwareList.Where(s => s.IsSelected).ToList();
        if (!selectedSoftware.Any()) return;

        try
        {
            StatusText = $"正在批量卸载 {selectedSoftware.Count} 个软件...";
            
            foreach (var software in selectedSoftware)
            {
                await _softwareService.UninstallSoftwareAsync(software);
                SoftwareList.Remove(software);
            }

            TotalSoftwareCount = SoftwareList.Count;
            UpdateStatusText();
            StatusText = $"批量卸载完成，共卸载 {selectedSoftware.Count} 个软件";
        }
        catch (Exception ex)
        {
            StatusText = $"批量卸载失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 导出软件列表
    /// </summary>
    [RelayCommand]
    private async Task ExportListAsync()
    {
        try
        {
            StatusText = "正在导出软件列表...";
            // 这里可以实现导出到HTML、CSV等格式的功能
            await Task.Delay(1000); // 模拟导出过程
            StatusText = "软件列表导出成功";
        }
        catch (Exception ex)
        {
            StatusText = $"导出失败: {ex.Message}";
        }
    }

    private void UpdateStatusText()
    {
        var totalSizeMB = SoftwareList.Sum(s => ParseSize(s.Size));
        TotalSize = totalSizeMB > 1024 ? $"{totalSizeMB / 1024:F1} GB" : $"{totalSizeMB:F0} MB";
    }

    private static double ParseSize(string sizeText)
    {
        if (string.IsNullOrEmpty(sizeText)) return 0;
        
        var parts = sizeText.Split(' ');
        if (parts.Length != 2) return 0;
        
        if (!double.TryParse(parts[0], out var size)) return 0;
        
        return parts[1].ToUpper() switch
        {
            "GB" => size * 1024,
            "MB" => size,
            "KB" => size / 1024,
            _ => 0
        };
    }
}
