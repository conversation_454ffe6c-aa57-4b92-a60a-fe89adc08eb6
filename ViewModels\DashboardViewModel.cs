using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace LSSOFT.ViewModels;

public partial class DashboardViewModel : ViewModelBase
{
    public ICommand NavigateToInstalledSoftwareCommand { get; }
    public ICommand NavigateToSoftwarePackagesCommand { get; }
    public ICommand NavigateToSoftwareLocalizationCommand { get; }
    public ICommand NavigateToSystemCleanupCommand { get; }

    public DashboardViewModel()
    {
        NavigateToInstalledSoftwareCommand = new RelayCommand(NavigateToInstalledSoftware);
        NavigateToSoftwarePackagesCommand = new RelayCommand(NavigateToSoftwarePackages);
        NavigateToSoftwareLocalizationCommand = new RelayCommand(NavigateToSoftwareLocalization);
        NavigateToSystemCleanupCommand = new RelayCommand(NavigateToSystemCleanup);
    }

    private void NavigateToInstalledSoftware()
    {
        // 通过消息传递或事件通知主窗口导航
        NavigationRequested?.Invoke("InstalledSoftware");
    }

    private void NavigateToSoftwarePackages()
    {
        NavigationRequested?.Invoke("SoftwarePackages");
    }

    private void NavigateToSoftwareLocalization()
    {
        NavigationRequested?.Invoke("SoftwareUpdates");
    }

    private void NavigateToSystemCleanup()
    {
        NavigationRequested?.Invoke("SystemCleanup");
    }

    // 导航请求事件
    public event System.Action<string>? NavigationRequested;
}
