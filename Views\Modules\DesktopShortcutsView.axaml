<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="LSSOFT.Views.Modules.DesktopShortcutsView"
             xmlns:vm="using:LSSOFT.ViewModels"
             xmlns:local="using:LSSOFT.Views.Modules"

             x:DataType="vm:DesktopShortcutsViewModel">

    <Design.DataContext>
        <vm:DesktopShortcutsViewModel />
    </Design.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="🚀" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="应用启动器" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 描述和工具栏 -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 描述文字 -->
            <TextBlock Grid.Column="0" Text="拖入应用图标，双击启动应用程序。"
                      FontSize="16" Foreground="#718096"
                      VerticalAlignment="Center"/>

            <!-- 搜索框和操作按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                <Button Content="➕ 添加"
                        ToolTip.Tip="添加自定义分类"
                        Classes="add-category-button"
                        Command="{Binding ShowAddCategoryDialogCommand}"
                        VerticalAlignment="Center"/>
                <TextBox Name="SearchBox"
                         Watermark="搜索应用..."
                         Width="150"
                         Text="{Binding SearchText}"
                         VerticalAlignment="Center"/>
                <Button Content="🔄"
                        ToolTip.Tip="刷新"
                        Command="{Binding RefreshCommand}"
                        Width="35" Height="35"
                        VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- 分类标签 -->
        <Grid Grid.Row="2" Margin="0,0,0,20">
            <!-- 分类按钮列表 - 支持多行显示 -->
            <ItemsControl ItemsSource="{Binding Categories}" MaxWidth="600" HorizontalAlignment="Left">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Button Content="{Binding Name}"
                                Classes="category-button"
                                Command="{Binding $parent[UserControl].DataContext.FilterByCategoryCommand}"
                                CommandParameter="{Binding Name}"
                                Classes.selected="{Binding IsSelected}"
                                Margin="0,0,5,5"/>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Grid>

        <!-- 主内容区域 -->
        <Border Grid.Row="3"
                Background="White"
                CornerRadius="8"
                BorderBrush="#E2E8F0"
                BorderThickness="1"
                Name="DropArea">

            <Grid>
                <!-- 加载指示器 -->
                <StackPanel IsVisible="{Binding IsLoading}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <TextBlock Text="⏳" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="正在加载应用..." FontSize="16" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- 空状态提示 -->
                <StackPanel IsVisible="{Binding IsEmpty}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Spacing="15">
                    <Border Width="120" Height="120"
                           Background="#F7FAFC"
                           CornerRadius="60"
                           BorderBrush="#E2E8F0"
                           BorderThickness="2">
                        <TextBlock Text="📱" FontSize="48"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"/>
                    </Border>

                    <StackPanel HorizontalAlignment="Center" Spacing="8">
                        <TextBlock Text="暂无应用"
                                  FontSize="18"
                                  FontWeight="SemiBold"
                                  Foreground="#2D3748"
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="拖拽应用程序(.exe)到此处添加快捷方式"
                                  FontSize="14"
                                  Foreground="#718096"
                                  HorizontalAlignment="Center"/>
                        <TextBlock Text="支持的文件类型：.exe, .lnk, .bat, .cmd"
                                  FontSize="12"
                                  Foreground="#A0AEC0"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>

                <!-- 应用图标网格 -->
                <ScrollViewer IsVisible="{Binding HasItems}"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Disabled">
                    <ItemsControl Name="AppIconsContainer"
                                 ItemsSource="{Binding FilteredShortcuts}"
                                 Margin="20">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>

                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Name="AppIconBorder"
                                       Width="80"
                                       Height="100"
                                       Margin="10"
                                       Background="Transparent"
                                       CornerRadius="8"
                                       Cursor="Hand"
                                       ToolTip.Tip="{Binding Name}">

                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="6">
                                        <!-- 应用图标 -->
                                        <Border Width="64" Height="64"
                                               CornerRadius="12"
                                               Background="Transparent"
                                               HorizontalAlignment="Center">
                                            <Grid>
                                                <!-- 真实图标 -->
                                                <Image Source="{Binding IconBitmap}"
                                                       Width="64" Height="64"
                                                       Stretch="Uniform"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       IsVisible="{Binding HasRealIcon}"/>

                                                <!-- 备用 Emoji 图标 -->
                                                <Border Width="64" Height="64"
                                                       CornerRadius="12"
                                                       Background="#F0F0F0"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"
                                                       IsVisible="{Binding !HasRealIcon}">
                                                    <TextBlock Text="{Binding IconText}"
                                                              FontSize="32"
                                                              HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                                </Border>
                                            </Grid>
                                        </Border>

                                        <!-- 应用名称 -->
                                        <TextBlock Text="{Binding DisplayName}"
                                                  FontSize="12"
                                                  FontWeight="Normal"
                                                  TextAlignment="Center"
                                                  TextWrapping="Wrap"
                                                  MaxWidth="76"
                                                  Foreground="#333333"
                                                  HorizontalAlignment="Center"
                                                  MaxLines="2"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- 状态栏 -->
        <Border Grid.Row="4"
                Background="#F7FAFC"
                BorderBrush="#E2E8F0"
                BorderThickness="0,1,0,0"
                Padding="15,10"
                Margin="0,10,0,0">
            <StackPanel Orientation="Horizontal" Spacing="10">
                <TextBlock Text="状态:" FontWeight="Medium" Foreground="#4A5568"/>
                <TextBlock Text="{Binding StatusText}" Foreground="#2D3748"/>
            </StackPanel>
        </Border>

        <!-- 添加分类对话框 -->
        <Border Grid.RowSpan="5"
                Background="#80000000"
                IsVisible="{Binding IsAddCategoryDialogVisible}">
            <Border Background="White"
                    CornerRadius="8"
                    BorderBrush="#E2E8F0"
                    BorderThickness="1"
                    Padding="20"
                    MaxWidth="400"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                <StackPanel Spacing="15">
                    <TextBlock Text="添加自定义分类"
                              FontSize="18"
                              FontWeight="SemiBold"
                              HorizontalAlignment="Center"/>

                    <TextBox Watermark="请输入分类名称..."
                            Text="{Binding NewCategoryName}"
                            Width="300"/>

                    <StackPanel Orientation="Horizontal"
                               HorizontalAlignment="Center"
                               Spacing="10">
                        <Button Content="确定"
                               Command="{Binding AddCategoryCommand}"
                               Classes="primary-button"
                               Width="80"/>
                        <Button Content="取消"
                               Command="{Binding CancelAddCategoryCommand}"
                               Classes="secondary-button"
                               Width="80"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Border>
    </Grid>

    <!-- 样式定义 -->
    <UserControl.Styles>
        <Style Selector="Button.category-button">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="0,0,5,0"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#4A5568"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style Selector="Button.category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC"/>
            <Setter Property="BorderBrush" Value="#CBD5E0"/>
        </Style>

        <Style Selector="Button.category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0"/>
        </Style>

        <!-- 选中状态样式 -->
        <Style Selector="Button.category-button.selected">
            <Setter Property="Background" Value="#3182CE"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#3182CE"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style Selector="Button.category-button.selected:pointerover">
            <Setter Property="Background" Value="#2C5AA0"/>
            <Setter Property="BorderBrush" Value="#2C5AA0"/>
        </Style>

        <!-- 添加分类按钮样式 -->
        <Style Selector="Button.add-category-button">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#4A5568"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style Selector="Button.add-category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC"/>
            <Setter Property="BorderBrush" Value="#CBD5E0"/>
        </Style>

        <Style Selector="Button.add-category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0"/>
        </Style>

        <Style Selector="Border#AppIconBorder:pointerover">
            <Setter Property="Background" Value="#F5F5F5"/>
        </Style>

        <Style Selector="Border#AppIconBorder:pressed">
            <Setter Property="Background" Value="#E8E8E8"/>
        </Style>

        <Style Selector="Border#DropArea">
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style Selector="Border#DropArea:dragover">
            <Setter Property="Background" Value="#E6FFFA"/>
            <Setter Property="BorderBrush" Value="#38B2AC"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <!-- 对话框按钮样式 -->
        <Style Selector="Button.primary-button">
            <Setter Property="Background" Value="#3182CE"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style Selector="Button.primary-button:pointerover">
            <Setter Property="Background" Value="#2C5AA0"/>
        </Style>

        <Style Selector="Button.secondary-button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#4A5568"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <Style Selector="Button.secondary-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC"/>
            <Setter Property="BorderBrush" Value="#CBD5E0"/>
        </Style>
    </UserControl.Styles>

</UserControl>
