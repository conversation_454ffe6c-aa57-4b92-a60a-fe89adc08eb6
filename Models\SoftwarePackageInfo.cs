using System;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;

namespace LSSOFT.Models;

/// <summary>
/// 软件包信息模型
/// </summary>
public partial class SoftwarePackageInfo : ObservableObject
{
    /// <summary>
    /// 软件包名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 软件包版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 最新版本
    /// </summary>
    public string LatestVersion { get; set; } = string.Empty;

    /// <summary>
    /// 软件包描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 软件包图片路径
    /// </summary>
    public string ImagePath { get; set; } = string.Empty;

    /// <summary>
    /// 软件包图标路径
    /// </summary>
    public string IconPath { get; set; } = string.Empty;

    /// <summary>
    /// 软件包分类
    /// </summary>
    public string Category { get; set; } = "未分类";

    /// <summary>
    /// 软件包分类标签列表
    /// </summary>
    public ObservableCollection<string> Categories { get; set; } = new ObservableCollection<string>();

    /// <summary>
    /// 软件包文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 软件包大小
    /// </summary>
    public string Size { get; set; } = string.Empty;

    /// <summary>
    /// 软件包类型
    /// </summary>
    public SoftwareType Type { get; set; }

    /// <summary>
    /// 是否已选中
    /// </summary>
    public bool IsSelected { get; set; }

    /// <summary>
    /// 是否可见（用于筛选）
    /// </summary>
    [ObservableProperty]
    private bool _isVisible = true;

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// 软件包内容（点击后显示的详细信息）
    /// </summary>
    public string Content { get; set; } = "这是内容";

    /// <summary>
    /// 许可证类型
    /// </summary>
    public string License { get; set; } = "CC BY-NC-ND 4.0";

    /// <summary>
    /// 评分/星级数量
    /// </summary>
    public string Stars { get; set; } = "0";

    /// <summary>
    /// 下载量
    /// </summary>
    public string Downloads { get; set; } = "0";

    /// <summary>
    /// 是否支持赞助
    /// </summary>
    public bool SupportDonation { get; set; } = false;

    /// <summary>
    /// 是否支持AI助手
    /// </summary>
    public bool SupportAI { get; set; } = false;

    /// <summary>
    /// 是否有更新
    /// </summary>
    public bool HasUpdate { get; set; } = false;

    /// <summary>
    /// 是否可破解
    /// </summary>
    public bool CanCrack { get; set; } = false;

    /// <summary>
    /// 是否开源
    /// </summary>
    public bool IsOpenSource { get; set; } = false;
}
