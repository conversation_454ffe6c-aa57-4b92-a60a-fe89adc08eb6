using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace LSSOFT.Services;

/// <summary>
/// 数据库服务，管理LSSOFT的配置和数据
/// </summary>
public class DatabaseService
{
    private readonly string _databasePath;
    private DatabaseModel? _database;

    public DatabaseService()
    {
        // 数据库文件路径
        _databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "lssoft.js");
    }

    /// <summary>
    /// 加载数据库
    /// </summary>
    public async Task<DatabaseModel> LoadDatabaseAsync()
    {
        try
        {
            if (_database != null)
                return _database;

            if (File.Exists(_databasePath))
            {
                var json = await File.ReadAllTextAsync(_databasePath, System.Text.Encoding.UTF8);
                _database = JsonSerializer.Deserialize<DatabaseModel>(json) ?? CreateDefaultDatabase();
            }
            else
            {
                _database = CreateDefaultDatabase();
                await SaveDatabaseAsync();
            }

            return _database;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载数据库失败: {ex.Message}");
            _database = CreateDefaultDatabase();
            return _database;
        }
    }

    /// <summary>
    /// 保存数据库
    /// </summary>
    public async Task SaveDatabaseAsync()
    {
        try
        {
            if (_database == null)
                return;

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_database, options);
            await File.WriteAllTextAsync(_databasePath, json, System.Text.Encoding.UTF8);
            
            System.Diagnostics.Debug.WriteLine($"数据库已保存到: {_databasePath}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存数据库失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取默认整理路径
    /// </summary>
    public async Task<string> GetDefaultOrganizationPathAsync()
    {
        var db = await LoadDatabaseAsync();
        return db.Settings.DefaultOrganizationPath;
    }

    /// <summary>
    /// 设置默认整理路径
    /// </summary>
    public async Task SetDefaultOrganizationPathAsync(string path)
    {
        var db = await LoadDatabaseAsync();
        db.Settings.DefaultOrganizationPath = path;
        await SaveDatabaseAsync();
        
        System.Diagnostics.Debug.WriteLine($"默认整理路径已更新为: {path}");
    }

    /// <summary>
    /// 获取软件分类列表
    /// </summary>
    public async Task<List<CategoryModel>> GetCategoriesAsync()
    {
        var db = await LoadDatabaseAsync();
        return db.Categories;
    }

    /// <summary>
    /// 添加新分类
    /// </summary>
    public async Task AddCategoryAsync(string categoryName)
    {
        var db = await LoadDatabaseAsync();

        // 检查是否已存在
        if (db.Categories.Any(c => c.Name == categoryName))
            return;

        // 添加新分类
        db.Categories.Add(new CategoryModel { Name = categoryName, Count = 0 });
        db.UpdatedAt = DateTime.Now;
        await SaveDatabaseAsync();

        System.Diagnostics.Debug.WriteLine($"已添加新分类: {categoryName}");
    }

    /// <summary>
    /// 更新分类计数
    /// </summary>
    public async Task UpdateCategoryCountAsync(string categoryName, int count)
    {
        var db = await LoadDatabaseAsync();
        var category = db.Categories.Find(c => c.Name == categoryName);
        if (category != null)
        {
            category.Count = count;
            await SaveDatabaseAsync();
        }
    }

    /// <summary>
    /// 更新分类名称
    /// </summary>
    public async Task UpdateCategoryNameAsync(string oldName, string newName)
    {
        var db = await LoadDatabaseAsync();
        var category = db.Categories.Find(c => c.Name == oldName);
        if (category != null)
        {
            category.Name = newName;
            db.UpdatedAt = DateTime.Now;
            await SaveDatabaseAsync();

            System.Diagnostics.Debug.WriteLine($"已更新分类名称: {oldName} -> {newName}");
        }
    }

    /// <summary>
    /// 删除分类
    /// </summary>
    public async Task DeleteCategoryAsync(string categoryName)
    {
        var db = await LoadDatabaseAsync();
        var category = db.Categories.Find(c => c.Name == categoryName);
        if (category != null)
        {
            db.Categories.Remove(category);

            // 同时删除相关的软件分类映射
            db.SoftwareCategoryMappings.RemoveAll(m => m.CategoryName == categoryName);

            db.UpdatedAt = DateTime.Now;
            await SaveDatabaseAsync();

            System.Diagnostics.Debug.WriteLine($"已删除分类: {categoryName}");
        }
    }

    /// <summary>
    /// 为软件分配分类
    /// </summary>
    public async Task AssignSoftwareCategoryAsync(string filePath, string categoryName)
    {
        var db = await LoadDatabaseAsync();

        // 移除该软件的现有分类映射
        db.SoftwareCategoryMappings.RemoveAll(m => m.FilePath == filePath);

        // 添加新的分类映射
        db.SoftwareCategoryMappings.Add(new SoftwareCategoryMapping
        {
            FilePath = filePath,
            CategoryName = categoryName,
            AssignedAt = DateTime.Now
        });

        db.UpdatedAt = DateTime.Now;
        await SaveDatabaseAsync();

        System.Diagnostics.Debug.WriteLine($"已为软件 {Path.GetFileName(filePath)} 分配分类: {categoryName}");
    }

    /// <summary>
    /// 获取软件的分类
    /// </summary>
    public async Task<string> GetSoftwareCategoryAsync(string filePath)
    {
        var db = await LoadDatabaseAsync();
        var mapping = db.SoftwareCategoryMappings.FirstOrDefault(m => m.FilePath == filePath);
        return mapping?.CategoryName ?? "未分类";
    }

    /// <summary>
    /// 获取指定分类下的所有软件
    /// </summary>
    public async Task<List<string>> GetSoftwareInCategoryAsync(string categoryName)
    {
        var db = await LoadDatabaseAsync();
        return db.SoftwareCategoryMappings
            .Where(m => m.CategoryName == categoryName)
            .Select(m => m.FilePath)
            .ToList();
    }

    /// <summary>
    /// 创建默认数据库
    /// </summary>
    private DatabaseModel CreateDefaultDatabase()
    {
        return new DatabaseModel
        {
            Version = "1.0.0",
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now,
            Settings = new SettingsModel
            {
                DefaultOrganizationPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + @"\Downloads"
            },
            Categories = new List<CategoryModel>
            {
                new CategoryModel { Name = "全部", Count = 0 },
                new CategoryModel { Name = "系统", Count = 0 },
                new CategoryModel { Name = "办公", Count = 0 },
                new CategoryModel { Name = "开发", Count = 0 },
                new CategoryModel { Name = "设计", Count = 0 },
                new CategoryModel { Name = "游戏", Count = 0 },
                new CategoryModel { Name = "手机", Count = 0 },
                new CategoryModel { Name = "其它", Count = 0 }
            }
        };
    }
}

/// <summary>
/// 数据库模型
/// </summary>
public class DatabaseModel
{
    public string Version { get; set; } = "1.0.0";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public SettingsModel Settings { get; set; } = new();
    public List<CategoryModel> Categories { get; set; } = new();
    public List<SoftwareCategoryMapping> SoftwareCategoryMappings { get; set; } = new();
}

/// <summary>
/// 设置模型
/// </summary>
public class SettingsModel
{
    public string DefaultOrganizationPath { get; set; } = "";
    public bool AutoScanOnStartup { get; set; } = true;
    public int MaxFilesPerScan { get; set; } = 1000;
    public string[] SupportedExtensions { get; set; } = { ".exe", ".msi", ".zip", ".rar", ".7z", ".tar", ".gz" };
}

/// <summary>
/// 分类模型
/// </summary>
public class CategoryModel
{
    public string Name { get; set; } = "";
    public int Count { get; set; } = 0;
}

/// <summary>
/// 软件分类映射模型
/// </summary>
public class SoftwareCategoryMapping
{
    public string FilePath { get; set; } = "";
    public string CategoryName { get; set; } = "";
    public DateTime AssignedAt { get; set; } = DateTime.Now;
}
