is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = LSSOFT
build_property.ProjectDir = E:\LSSOFT\LSSOFT\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[E:/LSSOFT/LSSOFT/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/ActivationHistoryView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/AutomationScriptsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/BatchTasksView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/CrackToolsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/DashboardView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/DesktopShortcutsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/DevelopmentToolsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/EngineeringSoftwareView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/GameSoftwareView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/InstalledSoftwareView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/LicenseManagerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/OfficeSoftwareView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/QuickLaunchView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/ScheduledTasksView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/ServiceManagerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/SoftwareLocalizationView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/SoftwarePackagesView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/StartMenuView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/StartupManagerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/LSSOFT/LSSOFT/Views/Modules/SystemCleanupView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
