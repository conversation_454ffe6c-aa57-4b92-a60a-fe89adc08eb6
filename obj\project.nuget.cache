{"version": 2, "dgSpecHash": "FjbiFG3uo2g=", "success": true, "projectFilePath": "E:\\LSSOFT\\LSSOFT\\LSSOFT.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\avalonia\\11.3.0\\avalonia.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.angle.windows.natives\\2.1.22045.20230930\\avalonia.angle.windows.natives.2.1.22045.20230930.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.buildservices\\0.0.31\\avalonia.buildservices.0.0.31.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.colorpicker\\11.3.0\\avalonia.controls.colorpicker.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.datagrid\\11.3.0\\avalonia.controls.datagrid.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.desktop\\11.3.0\\avalonia.desktop.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.diagnostics\\11.3.0\\avalonia.diagnostics.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.fonts.inter\\11.3.0\\avalonia.fonts.inter.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.freedesktop\\11.3.0\\avalonia.freedesktop.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.native\\11.3.0\\avalonia.native.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.remote.protocol\\11.3.0\\avalonia.remote.protocol.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.skia\\11.3.0\\avalonia.skia.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.fluent\\11.3.0\\avalonia.themes.fluent.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.simple\\11.3.0\\avalonia.themes.simple.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.win32\\11.3.0\\avalonia.win32.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.x11\\11.3.0\\avalonia.x11.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.4.0\\communitytoolkit.mvvm.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\7.3.0.3\\harfbuzzsharp.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.linux\\7.3.0.3\\harfbuzzsharp.nativeassets.linux.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\7.3.0.3\\harfbuzzsharp.nativeassets.macos.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.webassembly\\7.3.0.3\\harfbuzzsharp.nativeassets.webassembly.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\7.3.0.3\\harfbuzzsharp.nativeassets.win32.7.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microcom.runtime\\0.11.0\\microcom.runtime.0.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\9.0.5\\microsoft.win32.systemevents.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.9\\skiasharp.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux\\2.88.9\\skiasharp.nativeassets.linux.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.9\\skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.webassembly\\2.88.9\\skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.9\\skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\9.0.5\\system.drawing.common.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tmds.dbus.protocol\\0.21.2\\tmds.dbus.protocol.0.21.2.nupkg.sha512"], "logs": []}