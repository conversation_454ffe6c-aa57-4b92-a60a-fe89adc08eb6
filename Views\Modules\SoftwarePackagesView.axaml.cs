using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using LSSOFT.ViewModels;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace LSSOFT.Views.Modules;

public partial class SoftwarePackagesView : UserControl
{
    private Timer? _scrollBarHideTimer;
    private readonly object _timerLock = new object();

    public SoftwarePackagesView()
    {
        InitializeComponent();
        DataContext = new SoftwarePackagesViewModel();

        // 订阅滚动事件
        this.PointerWheelChanged += OnPointerWheelChanged;

        // 订阅鼠标进入和离开事件
        this.PointerEntered += OnPointerEntered;
        this.PointerExited += OnPointerExited;
    }

    private void OnPointerWheelChanged(object? sender, PointerWheelEventArgs e)
    {
        // 鼠标滚轮滚动时显示滚动条
        ShowScrollBar();

        // 重置隐藏计时器
        ResetHideTimer();
    }

    private void OnPointerEntered(object? sender, PointerEventArgs e)
    {
        // 鼠标进入时显示滚动条
        ShowScrollBar();
    }

    private void OnPointerExited(object? sender, PointerEventArgs e)
    {
        // 鼠标离开时启动隐藏计时器
        ResetHideTimer();
    }

    private void ShowScrollBar()
    {
        if (SoftwareScrollViewer != null)
        {
            SoftwareScrollViewer.VerticalScrollBarVisibility = ScrollBarVisibility.Auto;
        }
    }

    private void HideScrollBar()
    {
        if (SoftwareScrollViewer != null)
        {
            SoftwareScrollViewer.VerticalScrollBarVisibility = ScrollBarVisibility.Hidden;
        }
    }

    private void ResetHideTimer()
    {
        lock (_timerLock)
        {
            // 取消现有计时器
            _scrollBarHideTimer?.Dispose();

            // 创建新的计时器，2秒后隐藏滚动条
            _scrollBarHideTimer = new Timer(
                callback: _ =>
                {
                    Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        HideScrollBar();
                    });
                },
                state: null,
                dueTime: TimeSpan.FromSeconds(2),
                period: Timeout.InfiniteTimeSpan
            );
        }
    }

    protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
    {
        // 清理资源
        lock (_timerLock)
        {
            _scrollBarHideTimer?.Dispose();
            _scrollBarHideTimer = null;
        }

        base.OnDetachedFromVisualTree(e);
    }
}
