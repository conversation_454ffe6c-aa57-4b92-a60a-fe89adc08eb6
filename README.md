# 个人软件综合管理系统 (LSSOFT)

## 项目概述

这是一个基于 Avalonia UI 和 .NET 9.0 开发的个人软件综合管理系统，旨在帮助用户更好地管理计算机上的软件、快捷方式、系统优化等功能。

## 功能模块

### 🛠️ 主要功能

#### 📦 软件管理
- **已安装软件管理**: 查看、搜索、卸载已安装的软件
- **软件安装包管理**: 管理下载的软件安装包
- **软件更新**: 检查和管理软件更新

#### 🔗 快捷方式管理
- **桌面快捷方式**: 整理和分类桌面快捷方式
- **开始菜单管理**: 管理开始菜单项目和分组
- **快速启动栏**: 管理快速启动栏的应用程序

#### 🧹 系统精简
- **系统清理**: 清理系统垃圾文件和临时文件
- **启动项管理**: 管理系统启动项和开机自启程序
- **服务管理**: 管理Windows系统服务

#### 🔓 破解管理
- **破解工具**: 管理软件破解工具和资源
- **许可证管理**: 管理软件许可证和激活信息
- **激活记录**: 查看软件激活历史记录

#### ⚡ 流程管理
- **自动化脚本**: 创建和管理自动化脚本
- **批处理任务**: 管理批处理任务和脚本
- **定时任务**: 管理定时执行的任务

#### 🎯 分类管理
- **游戏软件**: 专门管理游戏软件和相关工具
- **工程软件**: 管理工程设计和CAD软件
- **办公软件**: 管理办公套件和生产力工具
- **开发工具**: 管理编程和开发相关工具

## 技术架构

### 技术栈
- **UI框架**: Avalonia UI 11.2.6
- **开发平台**: .NET 9.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: CommunityToolkit.Mvvm 8.2.1

### 项目结构
```
LSSOFT/
├── Models/                 # 数据模型
│   ├── SoftwareInfo.cs    # 软件信息模型
│   └── ShortcutInfo.cs    # 快捷方式信息模型
├── ViewModels/            # 视图模型
│   ├── MainWindowViewModel.cs
│   └── ViewModelBase.cs
├── Views/                 # 视图
│   ├── MainWindow.axaml   # 主窗口
│   └── Modules/           # 功能模块视图
│       ├── DashboardView.axaml
│       ├── InstalledSoftwareView.axaml
│       └── ...
├── Services/              # 服务层
│   └── SoftwareService.cs # 软件管理服务
└── Assets/               # 资源文件
```

## 主要特性

### 🎨 用户界面
- 现代化的 Material Design 风格界面
- 响应式布局，支持窗口缩放
- **自定义标题栏**: 35px高度，集成窗口控制按钮，支持拖拽移动
- **可折叠导航栏**: 启动时显示图标（50px），点击展开显示完整导航（200px）
- **24px导航图标**: 清晰的emoji图标，40px按钮高度，优化的触摸体验
- 直观的导航栏和模块化设计
- 丰富的图标和emoji表情支持
- 平滑的动画过渡效果

### 📊 仪表板
- 系统概览卡片显示
- 快速操作按钮
- 最近活动记录
- 系统信息展示

### 🔍 软件管理
- 支持软件搜索和筛选
- 按类型分类显示
- 批量操作支持
- 详细的软件信息展示

## 开发状态

### ✅ 已完成
- [x] 项目基础架构搭建
- [x] 主界面设计和布局
- [x] **可折叠导航系统实现**
- [x] **导航栏空间优化** (v1.1)
- [x] **自定义标题栏和窗口控制** (v1.2)
- [x] **导航图标尺寸优化** (v1.3)
- [x] **菜单按钮样式统一** (v1.4)
- [x] **菜单图标对齐修复** (v1.5)
- [x] 仪表板模块
- [x] 已安装软件管理界面
- [x] 数据模型定义
- [x] 基础服务层
- [x] MVVM架构实现
- [x] Emoji图标显示修复
- [x] 导航栏动画过渡效果

### 🚧 开发中
- [ ] 软件扫描和数据获取
- [ ] 快捷方式管理功能
- [ ] 系统清理功能
- [ ] 各模块具体功能实现

### 📋 计划中
- [ ] 数据持久化
- [ ] 配置管理
- [ ] 插件系统
- [ ] 多语言支持
- [ ] 主题切换

## 编译和运行

### 环境要求
- .NET 9.0 SDK
- Windows 10/11 (推荐)
- Visual Studio 2022 或 VS Code

### 编译步骤
```bash
# 克隆项目
git clone <repository-url>
cd LSSOFT

# 还原依赖
dotnet restore

# 编译项目
dotnet build

# 运行项目
dotnet run
```

## 问题修复记录

### Emoji显示问题修复
- **问题**: 主界面中的emoji图标显示为问号
- **原因**: 文件编码问题导致emoji字符损坏
- **解决**: 重新设置正确的emoji字符编码
- **影响文件**:
  - `Views/MainWindow.axaml` - 修复标题栏和导航栏emoji
  - 各模块视图文件 - 确保emoji正常显示

### 数据绑定问题修复
- **问题**: Avalonia编译错误，缺少x:DataType指令
- **解决**: 为DataGrid和DataTemplate添加正确的x:DataType属性
- **影响文件**: `Views/Modules/InstalledSoftwareView.axaml`

### 可折叠导航栏实现
- **功能**: 参考现代应用设计，实现启动时显示图标，点击后展开的导航栏
- **特性**:
  - 默认折叠状态（宽度60px），只显示图标和工具提示
  - 点击菜单按钮展开（宽度250px），显示完整文字导航
  - 平滑的动画过渡效果
  - 主导航按钮和子菜单的层级结构
  - 响应式设计，适配不同屏幕尺寸
- **实现文件**:
  - `ViewModels/MainWindowViewModel.cs` - 添加导航状态管理
  - `Views/MainWindow.axaml` - 重新设计导航栏布局和样式

### 导航栏空间优化 (v1.1)
- **问题**: 用户反馈左侧导航栏显示太宽，占用过多屏幕空间
- **优化措施**:
  - 折叠宽度从60px优化到50px，节省16.7%的空间
  - 展开宽度从250px优化到200px，减少20%的空间占用
  - 图标尺寸从20px调整到16px，字体大小相应缩小
  - 优化按钮内边距和间距，提高信息密度
  - 子菜单缩进从30px调整到20px
- **技术修复**: 修复了NavigationWidth属性的数据绑定问题，使用GridLength类型确保宽度设置生效
- **效果**: 在保持可读性的前提下，最大化内容显示区域，提供更紧凑的用户界面

### 自定义标题栏和窗口控制 (v1.2)
- **问题**: 用户希望移除默认的文件标题和图标，在标题栏中添加窗口控制按钮
- **实现功能**:
  - 移除了顶部的应用程序图标和标题文字
  - 移除了右侧的设置和帮助按钮
  - 实现了自定义标题栏（35px高度）
  - 添加了最小化、最大化/还原、关闭按钮
  - 支持拖拽移动窗口
  - 窗口控制按钮具有悬停和按下效果
- **技术特性**:
  - 使用`ExtendClientAreaToDecorationsHint="True"`启用自定义标题栏
  - 实现了窗口拖拽功能
  - 最大化按钮图标会根据窗口状态动态切换
  - 关闭按钮具有红色悬停效果
- **效果**: 提供更现代化的窗口外观，去除冗余信息，专注于功能性

### 导航图标尺寸优化 (v1.3)
- **问题**: 用户希望调整左侧导航栏图标大小为24x24像素
- **实现功能**:
  - 将所有主导航图标从16px调整为24px
  - 相应调整按钮高度从36px优化为40px
  - 优化图标与文字的间距（从6px调整为8px）
  - 保持按钮内边距和外边距的平衡
- **技术特性**:
  - 统一所有导航模块的图标尺寸
  - 保持图标清晰度和视觉一致性
  - 优化触摸友好的按钮尺寸
- **效果**: 提供更清晰的图标显示，改善用户界面的视觉层次和可用性

### 菜单按钮样式统一 (v1.4)
- **问题**: 菜单按钮的鼠标悬停效果与其他导航图标不一致，存在边框差异
- **实现功能**:
  - 移除菜单按钮的边框（BorderThickness从1改为0）
  - 统一悬停背景色为#E2E8F0（与主导航按钮一致）
  - 调整菜单图标大小从20px到24px（与其他图标一致）
  - 统一按钮高度为40px和圆角为4px
  - 优化按钮对齐方式和内容布局
- **技术特性**:
  - 移除了nav-toggle-button的边框样式
  - 统一了所有导航按钮的视觉效果
  - 保持了功能性的同时提升了一致性
- **效果**: 实现了完全一致的导航按钮交互体验，提升界面的专业性和统一性

### 菜单图标对齐修复 (v1.5)
- **问题**: 菜单按钮的☰图标在折叠和展开状态下对齐不一致，影响视觉效果
- **实现功能**:
  - 折叠状态：图标完全居中显示，与导航栏宽度对齐
  - 展开状态：图标与文字左对齐，与其他导航图标保持一致
  - 使用Grid布局替代StackPanel，支持条件显示
  - 通过IsVisible绑定实现状态切换
- **技术特性**:
  - 使用`{Binding !IsNavigationExpanded}`实现折叠状态显示
  - 使用`{Binding IsNavigationExpanded}`实现展开状态显示
  - Grid布局确保两种状态下的正确对齐
  - 保持24px图标尺寸和8px间距的一致性
- **效果**: 菜单图标在所有状态下都能正确对齐，提供完美的视觉一致性

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用 MIT 许可证。
