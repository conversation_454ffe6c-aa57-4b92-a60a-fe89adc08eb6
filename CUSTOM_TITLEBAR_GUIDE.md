# 自定义标题栏实现指南

## 概述

本文档详细说明了LSSOFT项目中自定义标题栏的实现方式，包括技术细节、功能特性和使用方法。

## 🎯 设计目标

- **简洁性**: 移除冗余的标题文字和图标
- **功能性**: 提供完整的窗口控制功能
- **现代化**: 符合现代应用程序的设计趋势
- **用户体验**: 直观的交互和视觉反馈

## 🔧 技术实现

### 窗口属性配置

```xml
<Window
    ExtendClientAreaToDecorationsHint="True"
    ExtendClientAreaChromeHints="NoChrome"
    ExtendClientAreaTitleBarHeightHint="35">
```

- `ExtendClientAreaToDecorationsHint="True"`: 启用自定义标题栏
- `ExtendClientAreaChromeHints="NoChrome"`: 移除默认窗口装饰
- `ExtendClientAreaTitleBarHeightHint="35"`: 设置标题栏高度为35px

### 标题栏布局

```xml
<Border Grid.Row="0" Background="#2D3748" BorderBrush="#4A5568" BorderThickness="0,0,0,1">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <!-- 可拖拽区域 -->
        <Border Grid.Column="0" Background="Transparent" Name="TitleBarDragArea" />

        <!-- 窗口控制按钮 -->
        <StackPanel Grid.Column="1" Orientation="Horizontal">
            <!-- 按钮内容 -->
        </StackPanel>
    </Grid>
</Border>
```

## 🎮 窗口控制按钮

### 按钮规格

| 按钮 | 图标 | 宽度 | 高度 | 功能 |
|------|------|------|------|------|
| 最小化 | 🗕 | 46px | 35px | 最小化窗口 |
| 最大化 | 🗖/🗗 | 46px | 35px | 最大化/还原窗口 |
| 关闭 | 🗙 | 46px | 35px | 关闭应用程序 |

### 交互效果

- **默认状态**: 透明背景
- **悬停状态**: 
  - 最小化/最大化: 深灰色背景 (#4A5568)
  - 关闭按钮: 红色背景 (#E53E3E)
- **按下状态**: 更深的背景色

## 💻 代码实现

### 事件处理

```csharp
private void SetupWindowControls()
{
    // 设置拖拽区域
    var titleBarDragArea = this.FindControl<Border>("TitleBarDragArea");
    if (titleBarDragArea != null)
    {
        titleBarDragArea.PointerPressed += TitleBarDragArea_PointerPressed;
    }

    // 设置窗口控制按钮事件
    var minimizeButton = this.FindControl<Button>("MinimizeButton");
    var maximizeButton = this.FindControl<Button>("MaximizeButton");
    var closeButton = this.FindControl<Button>("CloseButton");

    if (minimizeButton != null)
        minimizeButton.Click += MinimizeButton_Click;

    if (maximizeButton != null)
        maximizeButton.Click += MaximizeButton_Click;

    if (closeButton != null)
        closeButton.Click += CloseButton_Click;
}
```

### 拖拽功能

```csharp
private void TitleBarDragArea_PointerPressed(object? sender, PointerPressedEventArgs e)
{
    if (e.GetCurrentPoint(this).Properties.IsLeftButtonPressed)
    {
        BeginMoveDrag(e);
    }
}
```

### 最大化状态切换

```csharp
private void MaximizeButton_Click(object? sender, RoutedEventArgs e)
{
    WindowState = WindowState == WindowState.Maximized
        ? WindowState.Normal
        : WindowState.Maximized;

    // 更新最大化按钮图标
    var maximizeButton = this.FindControl<Button>("MaximizeButton");
    if (maximizeButton?.Content is TextBlock textBlock)
    {
        textBlock.Text = WindowState == WindowState.Maximized ? "🗗" : "🗖";
    }
}
```

## 🎨 样式定义

### 按钮样式

```xml
<!-- 最小化和最大化按钮 -->
<Style Selector="Button#MinimizeButton:pointerover">
    <Setter Property="Background" Value="#4A5568" />
</Style>

<!-- 关闭按钮特殊样式 -->
<Style Selector="Button#CloseButton:pointerover">
    <Setter Property="Background" Value="#E53E3E" />
</Style>
```

## 📱 响应式设计

- **标题栏高度**: 35px，适合触摸操作
- **按钮尺寸**: 46x35px，符合人机工程学
- **拖拽区域**: 占据标题栏大部分区域，便于窗口移动

## 🔍 功能测试

### 测试项目

- [ ] 窗口拖拽功能正常
- [ ] 最小化按钮工作正常
- [ ] 最大化/还原切换正常
- [ ] 关闭按钮功能正常
- [ ] 按钮悬停效果正确
- [ ] 最大化图标动态切换

### 兼容性

- ✅ Windows 10/11
- ✅ 高DPI显示器
- ✅ 多显示器环境

## 🚀 未来改进

- [ ] 添加双击标题栏最大化功能
- [ ] 支持右键菜单
- [ ] 添加窗口动画效果
- [ ] 支持主题切换

## 📝 注意事项

1. **性能**: 自定义标题栏可能略微影响性能，但在现代硬件上影响微乎其微
2. **兼容性**: 某些系统主题可能不完全兼容
3. **可访问性**: 确保按钮具有适当的工具提示和键盘导航支持

---

*此自定义标题栏实现提供了现代化的用户界面，同时保持了完整的窗口控制功能。*
