using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace LSSOFT.Converters;

/// <summary>
/// 布尔值到颜色的转换器
/// </summary>
public class BoolToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isSelected)
        {
            return isSelected ? Colors.DodgerBlue : Colors.LightGray;
        }
        
        return Colors.LightGray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
