﻿using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using System.Collections.Generic;
using LSSOFT.ViewModels;

namespace LSSOFT.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        SetupWindowControls();
        SetupNavigationWheelEvents();
    }

    private void SetupWindowControls()
    {
        // ������ק����
        var titleBarDragArea = this.FindControl<Border>("TitleBarDragArea");
        if (titleBarDragArea != null)
        {
            titleBarDragArea.PointerPressed += TitleBarDragArea_PointerPressed;
        }

        // ���ô��ڿ��ư�ť�¼�
        var minimizeButton = this.FindControl<Button>("MinimizeButton");
        var maximizeButton = this.FindControl<Button>("MaximizeButton");
        var closeButton = this.FindControl<Button>("CloseButton");

        if (minimizeButton != null)
            minimizeButton.Click += MinimizeButton_Click;

        if (maximizeButton != null)
            maximizeButton.Click += MaximizeButton_Click;

        if (closeButton != null)
            closeButton.Click += CloseButton_Click;
    }

    private void TitleBarDragArea_PointerPressed(object? sender, PointerPressedEventArgs e)
    {
        if (e.GetCurrentPoint(this).Properties.IsLeftButtonPressed)
        {
            BeginMoveDrag(e);
        }
    }

    private void MinimizeButton_Click(object? sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object? sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized
            ? WindowState.Normal
            : WindowState.Maximized;

        // ������󻯰�ťͼ��
        var maximizeButton = this.FindControl<Button>("MaximizeButton");
        if (maximizeButton?.Content is TextBlock textBlock)
        {
            textBlock.Text = WindowState == WindowState.Maximized ? "�9�5" : "�9�4";
        }
    }

    private void CloseButton_Click(object? sender, RoutedEventArgs e)
    {
        Close();
    }

    private void SetupNavigationWheelEvents()
    {
        // 为导航面板添加滚轮事件
        var navigationPanel = this.FindControl<Border>("NavigationPanel");
        if (navigationPanel != null)
        {
            navigationPanel.PointerWheelChanged += NavigationPanel_PointerWheelChanged;
        }
    }

    private void NavigationPanel_PointerWheelChanged(object? sender, PointerWheelEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            if (e.Delta.Y > 0)
            {
                // 向上滚动 - 切换到上一个菜单
                viewModel.ScrollUpCommand.Execute(null);
            }
            else if (e.Delta.Y < 0)
            {
                // 向下滚动 - 切换到下一个菜单
                viewModel.ScrollDownCommand.Execute(null);
            }
        }

        e.Handled = true;
    }
}