<Window
    x:Class="LSSOFT.Views.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="using:LSSOFT.ViewModels"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="clr-namespace:Avalonia.Controls;assembly=Avalonia.Controls.DataGrid"
    Title="个人软件综合管理系统" MinWidth="1000"
    MinHeight="600" d:DesignWidth="1200"
    d:DesignHeight="800"
    x:DataType="vm:MainWindowViewModel"
    WindowStartupLocation="CenterScreen"
    ExtendClientAreaToDecorationsHint="True"
    ExtendClientAreaChromeHints="NoChrome"
    ExtendClientAreaTitleBarHeightHint="35"
    Icon="avares://LSSOFT/Assets/lssoft.ico"
    mc:Ignorable="d">

    <Design.DataContext>
        <vm:MainWindowViewModel />
    </Design.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
            <RowDefinition Height="30" />
        </Grid.RowDefinitions>

        <!--  自定义标题栏  -->
        <Border Grid.Row="0"
            Background="#F8F9FA" BorderBrush="Transparent"
            BorderThickness="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  应用程序图标和标题  -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="8,0,0,0">
                    <Image Source="avares://LSSOFT/Assets/lssoft.ico" Width="20" Height="20" Margin="0,0,8,0" />
                    <TextBlock Text="个人软件综合管理系统" VerticalAlignment="Center" FontSize="14" Foreground="#495057" />
                </StackPanel>

                <!--  可拖拽区域  -->
                <Border Name="TitleBarDragArea" Grid.Column="1" Background="Transparent" />

                <!--  窗口控制按钮  -->
                <StackPanel Grid.Column="2"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top" Orientation="Horizontal">
                    <!--  最小化按钮  -->
                    <Button
                        Name="MinimizeButton" Width="46"
                        Height="35" Background="Transparent"
                        BorderThickness="0" ToolTip.Tip="最小化">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" Text="—"
                            FontSize="12" Foreground="#495057" />
                    </Button>

                    <!--  最大化/还原按钮  -->
                    <Button
                        Name="MaximizeButton" Width="46"
                        Height="35" Background="Transparent"
                        BorderThickness="0" ToolTip.Tip="最大化">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" Text="🗖"
                            FontSize="12" Foreground="#495057" />
                    </Button>

                    <!--  关闭按钮  -->
                    <Button
                        Name="CloseButton" Width="46"
                        Height="35" Background="Transparent"
                        BorderThickness="0" ToolTip.Tip="关闭">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" Text="🗙"
                            FontSize="12" Foreground="#495057" />
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!--  主内容区域  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding NavigationWidth}" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧导航栏  -->
            <Border Grid.Column="0"
                Name="NavigationPanel" Background="#F8F9FA"
                BorderBrush="Transparent" BorderThickness="0">
                <Border.Transitions>
                    <Transitions>
                        <DoubleTransition Easing="CubicEaseInOut" Property="Width" Duration="0:0:0.3" />
                    </Transitions>
                </Border.Transitions>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  导航栏切换按钮 - 位于搜索框上方  -->
                    <Button Grid.Row="0"
                        Name="NavToggleButton" Height="32"
                        Margin="8,0,8,0" HorizontalAlignment="Stretch"
                        Background="Transparent" BorderThickness="0"
                        Command="{Binding ToggleNavigationCommand}"
                        ToolTip.Tip="展开/收缩导航栏">
                        <Grid>
                            <!--  折叠状态：图标居中  -->
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center" Text="☰"
                                FontSize="18" Foreground="#495057"
                                IsVisible="{Binding !IsNavigationExpanded}" />

                            <!--  展开状态：图标和文字  -->
                            <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                <TextBlock
                                    Margin="0,0,12,0" VerticalAlignment="Center"
                                    Text="☰" FontSize="18"
                                    Foreground="#495057" />
                                <TextBlock
                                    VerticalAlignment="Center" Text="导航"
                                    FontSize="14" Foreground="#495057" />
                            </StackPanel>
                        </Grid>
                    </Button>

                    <!--  搜索框  -->
                    <Border Grid.Row="1" Margin="12,0,12,2" IsVisible="{Binding IsNavigationExpanded}">
                        <TextBox
                            Name="NavSearchBox" Padding="12,8"
                            Watermark="Search" Background="White"
                            BorderBrush="#DEE2E6" BorderThickness="1"
                            CornerRadius="6" FontSize="14" />
                    </Border>

                    <!--  导航内容  -->
                    <ScrollViewer Grid.Row="2" Margin="0,0,0,2">
                        <StackPanel Margin="8,0">
                            <!--  主页  -->
                            <Button
                                Margin="0,1,0,1" Classes="nav-item-button"
                                Classes.active="{Binding IsHomeActive}"
                                Command="{Binding NavigateToHomeCommand}">
                                <Grid>
                                    <!--  折叠状态：图标居中  -->
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center" Text="🏠"
                                        FontSize="18"
                                        IsVisible="{Binding !IsNavigationExpanded}" />

                                    <!--  展开状态：图标和文字  -->
                                    <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                        <TextBlock
                                            Margin="0,0,12,0" VerticalAlignment="Center"
                                            Text="🏠" FontSize="18" />
                                        <TextBlock
                                            VerticalAlignment="Center" Text="主页"
                                            FontSize="14" Foreground="#495057" />
                                    </StackPanel>
                                </Grid>
                            </Button>

                            <!--  软件管理 - 可折叠  -->
                            <StackPanel>
                                <!--  主按钮  -->
                                <Button
                                    Margin="0,1,0,1" Classes="nav-expandable-button"
                                    Classes.active="{Binding IsSoftwareManagementActive}"
                                    Command="{Binding NavigateToSoftwareManagementCommand}">
                                    <Grid>
                                        <!--  折叠状态：图标居中  -->
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center" Text="📦"
                                            FontSize="18"
                                            IsVisible="{Binding !IsNavigationExpanded}" />

                                        <!--  展开状态：图标、文字和箭头  -->
                                        <Grid IsVisible="{Binding IsNavigationExpanded}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                Margin="12,0,12,0" VerticalAlignment="Center"
                                                Text="📦" FontSize="18" />
                                            <TextBlock Grid.Column="1"
                                                VerticalAlignment="Center" Text="软件管理"
                                                FontSize="14" Foreground="#495057" />
                                            <TextBlock Grid.Column="2"
                                                Margin="0,0,12,0" VerticalAlignment="Center"
                                                Text="❯" FontSize="12"
                                                Foreground="#6C757D">
                                                <TextBlock.RenderTransform>
                                                    <RotateTransform Angle="{Binding SoftwareManagementRotation}" />
                                                </TextBlock.RenderTransform>
                                            </TextBlock>
                                        </Grid>
                                    </Grid>
                                </Button>

                                <!--  子菜单 - 只在导航栏和子菜单都展开时显示  -->
                                <StackPanel Margin="44,4,0,0">
                                    <StackPanel.IsVisible>
                                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                                            <Binding Path="IsNavigationExpanded" />
                                            <Binding Path="IsSoftwareManagementExpanded" />
                                        </MultiBinding>
                                    </StackPanel.IsVisible>
                                    <Button
                                        Content="卸载清理" Classes="nav-sub-button"
                                        Classes.active="{Binding IsInstalledSoftwareActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="InstalledSoftware" />
                                    <Button
                                        Content="软件安装" Classes="nav-sub-button"
                                        Classes.active="{Binding IsSoftwarePackagesActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="SoftwarePackages" />
                                    <Button
                                        Content="软件整理" Classes="nav-sub-button"
                                        Classes.active="{Binding IsSoftwareUpdatesActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="SoftwareUpdates" />
                                </StackPanel>
                            </StackPanel>

                            <!--  快捷方式管理 - 可折叠  -->
                            <StackPanel>
                                <!--  主按钮  -->
                                <Button
                                    Margin="0,1,0,1" Classes="nav-expandable-button"
                                    Classes.active="{Binding IsShortcutManagementActive}"
                                    Command="{Binding NavigateToShortcutManagementCommand}">
                                    <Grid>
                                        <!--  折叠状态：图标居中  -->
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center" Text="🔗"
                                            FontSize="18"
                                            IsVisible="{Binding !IsNavigationExpanded}" />

                                        <!--  展开状态：图标、文字和箭头  -->
                                        <Grid IsVisible="{Binding IsNavigationExpanded}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                Margin="12,0,12,0" VerticalAlignment="Center"
                                                Text="🔗" FontSize="18" />
                                            <TextBlock Grid.Column="1"
                                                VerticalAlignment="Center" Text="快捷方式"
                                                FontSize="14" Foreground="#495057" />
                                            <TextBlock Grid.Column="2"
                                                Margin="0,0,12,0" VerticalAlignment="Center"
                                                Text="❯" FontSize="12"
                                                Foreground="#6C757D">
                                                <TextBlock.RenderTransform>
                                                    <RotateTransform Angle="{Binding ShortcutManagementRotation}" />
                                                </TextBlock.RenderTransform>
                                            </TextBlock>
                                        </Grid>
                                    </Grid>
                                </Button>

                                <!--  子菜单 - 只在导航栏和子菜单都展开时显示  -->
                                <StackPanel Margin="44,4,0,0">
                                    <StackPanel.IsVisible>
                                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                                            <Binding Path="IsNavigationExpanded" />
                                            <Binding Path="IsShortcutManagementExpanded" />
                                        </MultiBinding>
                                    </StackPanel.IsVisible>
                                    <Button
                                        Content="桌面快捷方式" Classes="nav-sub-button"
                                        Classes.active="{Binding IsDesktopShortcutsActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="DesktopShortcuts" />
                                    <Button
                                        Content="开始菜单" Classes="nav-sub-button"
                                        Classes.active="{Binding IsStartMenuActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="StartMenu" />
                                    <Button
                                        Content="快速启动栏" Classes="nav-sub-button"
                                        Classes.active="{Binding IsQuickLaunchActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="QuickLaunch" />
                                </StackPanel>
                            </StackPanel>

                            <!--  系统精简 - 可折叠  -->
                            <StackPanel>
                                <!--  主按钮  -->
                                <Button
                                    Margin="0,1,0,1" Classes="nav-expandable-button"
                                    Classes.active="{Binding IsSystemCleanupActive}"
                                    Command="{Binding NavigateToSystemCleanupCommand}">
                                    <Grid>
                                        <!--  折叠状态：图标居中  -->
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center" Text="🧹"
                                            FontSize="18"
                                            IsVisible="{Binding !IsNavigationExpanded}" />

                                        <!--  展开状态：图标、文字和箭头  -->
                                        <Grid IsVisible="{Binding IsNavigationExpanded}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                Margin="12,0,12,0" VerticalAlignment="Center"
                                                Text="🧹" FontSize="18" />
                                            <TextBlock Grid.Column="1"
                                                VerticalAlignment="Center" Text="系统精简"
                                                FontSize="14" Foreground="#495057" />
                                            <TextBlock Grid.Column="2"
                                                Margin="0,0,12,0" VerticalAlignment="Center"
                                                Text="❯" FontSize="12"
                                                Foreground="#6C757D">
                                                <TextBlock.RenderTransform>
                                                    <RotateTransform Angle="{Binding SystemCleanupRotation}" />
                                                </TextBlock.RenderTransform>
                                            </TextBlock>
                                        </Grid>
                                    </Grid>
                                </Button>

                                <!--  子菜单 - 只在导航栏和子菜单都展开时显示  -->
                                <StackPanel Margin="44,4,0,0">
                                    <StackPanel.IsVisible>
                                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                                            <Binding Path="IsNavigationExpanded" />
                                            <Binding Path="IsSystemCleanupExpanded" />
                                        </MultiBinding>
                                    </StackPanel.IsVisible>
                                    <Button
                                        Content="系统清理" Classes="nav-sub-button"
                                        Classes.active="{Binding IsSystemCleanupPageActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="SystemCleanup" />
                                    <Button
                                        Content="启动项管理" Classes="nav-sub-button"
                                        Classes.active="{Binding IsStartupManagerActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="StartupManager" />
                                    <Button
                                        Content="服务管理" Classes="nav-sub-button"
                                        Classes.active="{Binding IsServiceManagerActive}"
                                        Command="{Binding NavigateCommand}"
                                        CommandParameter="ServiceManager" />
                                </StackPanel>
                            </StackPanel>

                            <!--  破解管理  -->
                            <Button
                                Margin="0,1,0,1" Classes="nav-item-button"
                                Classes.active="{Binding IsCrackToolsActive}"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="CrackTools">
                                <Grid>
                                    <!--  折叠状态：图标居中  -->
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center" Text="🔓"
                                        FontSize="18"
                                        IsVisible="{Binding !IsNavigationExpanded}" />

                                    <!--  展开状态：图标和文字  -->
                                    <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                        <TextBlock
                                            Margin="0,0,12,0" VerticalAlignment="Center"
                                            Text="🔓" FontSize="18" />
                                        <TextBlock
                                            VerticalAlignment="Center" Text="破解管理"
                                            FontSize="14" Foreground="#495057" />
                                    </StackPanel>
                                </Grid>
                            </Button>

                            <!--  自动化  -->
                            <Button
                                Margin="0,1,0,1" Classes="nav-item-button"
                                Classes.active="{Binding IsAutomationActive}"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="AutomationScripts">
                                <Grid>
                                    <!--  折叠状态：图标居中  -->
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center" Text="⚡"
                                        FontSize="18"
                                        IsVisible="{Binding !IsNavigationExpanded}" />

                                    <!--  展开状态：图标和文字  -->
                                    <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                        <TextBlock
                                            Margin="0,0,12,0" VerticalAlignment="Center"
                                            Text="⚡" FontSize="18" />
                                        <TextBlock
                                            VerticalAlignment="Center" Text="自动化"
                                            FontSize="14" Foreground="#495057" />
                                    </StackPanel>
                                </Grid>
                            </Button>

                            <!--  其他工具  -->
                            <Button
                                Margin="0,1,0,1" Classes="nav-item-button"
                                Classes.active="{Binding IsGameSoftwareActive}"
                                Command="{Binding NavigateCommand}"
                                CommandParameter="GameSoftware">
                                <Grid>
                                    <!--  折叠状态：图标居中  -->
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center" Text="🎯"
                                        FontSize="18"
                                        IsVisible="{Binding !IsNavigationExpanded}" />

                                    <!--  展开状态：图标和文字  -->
                                    <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                        <TextBlock
                                            Margin="0,0,12,0" VerticalAlignment="Center"
                                            Text="🎯" FontSize="18" />
                                        <TextBlock
                                            VerticalAlignment="Center" Text="其他工具"
                                            FontSize="14" Foreground="#495057" />
                                    </StackPanel>
                                </Grid>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>

                    <!--  设置按钮 - 底部固定  -->
                    <Button Grid.Row="3" Margin="8,2,8,4" Classes="nav-item-button">
                        <Grid>
                            <!--  折叠状态：图标居中  -->
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center" Text="⚙️"
                                FontSize="18"
                                IsVisible="{Binding !IsNavigationExpanded}" />

                            <!--  展开状态：图标和文字  -->
                            <StackPanel Margin="12,0" Orientation="Horizontal" IsVisible="{Binding IsNavigationExpanded}">
                                <TextBlock
                                    Margin="0,0,12,0" VerticalAlignment="Center"
                                    Text="⚙️" FontSize="18" />
                                <TextBlock
                                    VerticalAlignment="Center" Text="设置"
                                    FontSize="14" Foreground="#495057" />
                            </StackPanel>
                        </Grid>
                    </Button>
                </Grid>
            </Border>


            <!--  主内容面板，显示当前视图  -->
            <ContentControl Grid.Column="1" Content="{Binding CurrentView}" Background="{DynamicResource ThemeBackgroundBrush}" />

        </Grid>

        <!--  状态栏  -->
        <Border Grid.Row="2"
            Background="#E2E8F0" BorderBrush="#CBD5E0"
            BorderThickness="0,1,0,0">
            <Grid VerticalAlignment="Center">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  左侧状态信息  -->
                <StackPanel Grid.Column="0"
                    Margin="10,0,0,0" Orientation="Horizontal"
                    Spacing="10">
                    <TextBlock Text="{Binding StatusMessage}" />
                </StackPanel>

                <!--  右侧时间  -->
                <TextBlock Grid.Column="1"
                    Margin="0,0,10,0" HorizontalAlignment="Right"
                    Text="{Binding CurrentTime}" />
            </Grid>
        </Border>
    </Grid>

    <Window.Styles>
        <!--  导航项按钮样式  -->
        <Style Selector="Button.nav-item-button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Padding" Value="0" />
            <Setter Property="Margin" Value="0" />
            <Setter Property="MinHeight" Value="38" />
            <Setter Property="CornerRadius" Value="8" />
        </Style>
        <Style Selector="Button.nav-item-button:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>
        <Style Selector="Button.nav-item-button:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <!--  激活状态样式 - 使用与悬停相同的颜色  -->
        <Style Selector="Button.nav-item-button.active">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>

        <!--  可展开导航按钮样式  -->
        <Style Selector="Button.nav-expandable-button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Padding" Value="0" />
            <Setter Property="Margin" Value="0" />
            <Setter Property="MinHeight" Value="38" />
            <Setter Property="CornerRadius" Value="8" />
        </Style>
        <Style Selector="Button.nav-expandable-button:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>
        <Style Selector="Button.nav-expandable-button:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <!--  可展开按钮激活状态样式 - 使用与悬停相同的颜色  -->
        <Style Selector="Button.nav-expandable-button.active">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>

        <!--  子导航按钮样式  -->
        <Style Selector="Button.nav-sub-button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="HorizontalContentAlignment" Value="Left" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="0,2" />
            <Setter Property="FontSize" Value="13" />
            <Setter Property="Foreground" Value="#6C757D" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="MinHeight" Value="36" />
        </Style>
        <Style Selector="Button.nav-sub-button:pointerover">
            <Setter Property="Background" Value="#F8F9FA" />
            <Setter Property="Foreground" Value="#495057" />
        </Style>
        <Style Selector="Button.nav-sub-button:pressed">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>

        <!--  子菜单按钮激活状态样式  -->
        <Style Selector="Button.nav-sub-button.active">
            <Setter Property="Background" Value="#F8F9FA" />
            <Setter Property="Foreground" Value="#495057" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <!--  保留原有样式以兼容  -->
        <Style Selector="Button.nav-button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="HorizontalContentAlignment" Value="Left" />
            <Setter Property="Padding" Value="10,8" />
            <Setter Property="Margin" Value="0,1" />
            <Setter Property="FontSize" Value="13" />
        </Style>
        <Style Selector="Button.nav-button:pointerover">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>
        <Style Selector="Button.nav-button:pressed">
            <Setter Property="Background" Value="#CBD5E0" />
        </Style>

        <!--  导航栏切换按钮样式  -->
        <Style Selector="Button#NavToggleButton">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="8" />
        </Style>
        <Style Selector="Button#NavToggleButton:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>
        <Style Selector="Button#NavToggleButton:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <!--  窗口控制按钮样式  -->
        <Style Selector="Button#MinimizeButton">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="4" />
        </Style>
        <Style Selector="Button#MinimizeButton:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>
        <Style Selector="Button#MinimizeButton:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <Style Selector="Button#MaximizeButton">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="4" />
        </Style>
        <Style Selector="Button#MaximizeButton:pointerover">
            <Setter Property="Background" Value="#E9ECEF" />
        </Style>
        <Style Selector="Button#MaximizeButton:pressed">
            <Setter Property="Background" Value="#DEE2E6" />
        </Style>

        <Style Selector="Button#CloseButton">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="4" />
        </Style>
        <Style Selector="Button#CloseButton:pointerover">
            <Setter Property="Background" Value="#DC3545" />
        </Style>
        <Style Selector="Button#CloseButton:pressed">
            <Setter Property="Background" Value="#C82333" />
        </Style>
    </Window.Styles>

</Window>
