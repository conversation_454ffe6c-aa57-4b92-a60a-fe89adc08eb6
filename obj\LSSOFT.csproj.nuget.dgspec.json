{"format": 1, "restore": {"E:\\LSSOFT\\LSSOFT\\LSSOFT.csproj": {}}, "projects": {"E:\\LSSOFT\\LSSOFT\\LSSOFT.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\LSSOFT\\LSSOFT\\LSSOFT.csproj", "projectName": "LSSOFT", "projectPath": "E:\\LSSOFT\\LSSOFT\\LSSOFT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\LSSOFT\\LSSOFT\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Controls.DataGrid": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.0, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.0, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}}