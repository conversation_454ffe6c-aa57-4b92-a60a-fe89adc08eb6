using System;

namespace LSSOFT.Models;

/// <summary>
/// 软件信息模型
/// </summary>
public class SoftwareInfo
{
    /// <summary>
    /// 软件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 软件版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 发布商
    /// </summary>
    public string Publisher { get; set; } = string.Empty;

    /// <summary>
    /// 安装日期
    /// </summary>
    public DateTime InstallDate { get; set; }

    /// <summary>
    /// 软件大小
    /// </summary>
    public string Size { get; set; } = string.Empty;

    /// <summary>
    /// 安装路径
    /// </summary>
    public string InstallPath { get; set; } = string.Empty;

    /// <summary>
    /// 软件图标路径
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 软件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 软件类型
    /// </summary>
    public SoftwareType Type { get; set; }

    /// <summary>
    /// 是否为系统软件
    /// </summary>
    public bool IsSystemSoftware { get; set; }

    /// <summary>
    /// 卸载命令
    /// </summary>
    public string UninstallCommand { get; set; } = string.Empty;

    /// <summary>
    /// 启动命令
    /// </summary>
    public string LaunchCommand { get; set; } = string.Empty;

    /// <summary>
    /// 是否已选中（用于批量操作）
    /// </summary>
    public bool IsSelected { get; set; }

    /// <summary>
    /// 安装日期字符串
    /// </summary>
    public string InstallDateString => InstallDate == DateTime.MinValue ? "" : InstallDate.ToString("yyyy-MM-dd");

    /// <summary>
    /// 软件类型字符串
    /// </summary>
    public string TypeString => Type.ToString();
}

/// <summary>
/// 软件类型枚举
/// </summary>
public enum SoftwareType
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown,

    /// <summary>
    /// 系统软件
    /// </summary>
    System,

    /// <summary>
    /// 办公软件
    /// </summary>
    Office,

    /// <summary>
    /// 开发工具
    /// </summary>
    Development,

    /// <summary>
    /// 游戏
    /// </summary>
    Game,

    /// <summary>
    /// 工程软件
    /// </summary>
    Engineering,

    /// <summary>
    /// 多媒体
    /// </summary>
    Multimedia,

    /// <summary>
    /// 网络工具
    /// </summary>
    Network,

    /// <summary>
    /// 安全软件
    /// </summary>
    Security,

    /// <summary>
    /// 其他
    /// </summary>
    Other
}
