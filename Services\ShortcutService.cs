using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LSSOFT.Models;
#if WINDOWS
using Microsoft.Win32;
using System.Drawing;
using System.Runtime.InteropServices;
#endif

namespace LSSOFT.Services;

/// <summary>
/// 快捷方式管理服务
/// </summary>
public class ShortcutService
{
    /// <summary>
    /// 获取桌面快捷方式列表
    /// </summary>
    /// <returns>快捷方式列表</returns>
    public async Task<List<ShortcutInfo>> GetDesktopShortcutsAsync()
    {
        var shortcuts = new List<ShortcutInfo>();

        return await Task.Run(() =>
        {
            try
            {
                // 获取桌面路径
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var publicDesktopPath = Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory);

                // 扫描用户桌面
                ScanDesktopFolder(desktopPath, shortcuts);

                // 扫描公共桌面
                ScanDesktopFolder(publicDesktopPath, shortcuts);

                // 不自动加载示例数据，等待用户拖入

                // 去重并排序
                var uniqueShortcuts = shortcuts
                    .GroupBy(s => s.Name.ToLower())
                    .Select(g => g.First())
                    .OrderBy(s => s.Name)
                    .ToList();

                Debug.WriteLine($"找到 {uniqueShortcuts.Count} 个桌面快捷方式");
                return uniqueShortcuts;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取桌面快捷方式失败: {ex.Message}");
                // 返回空列表，等待用户拖入
                return shortcuts;
            }
        });
    }
    
    /// <summary>
    /// 扫描桌面文件夹
    /// </summary>
    /// <param name="folderPath">文件夹路径</param>
    /// <param name="shortcuts">快捷方式列表</param>
    private void ScanDesktopFolder(string folderPath, List<ShortcutInfo> shortcuts)
    {
        if (!Directory.Exists(folderPath))
            return;
            
        try
        {
            // 获取所有快捷方式文件
            var shortcutFiles = Directory.GetFiles(folderPath, "*.lnk", SearchOption.TopDirectoryOnly);
            
            foreach (var file in shortcutFiles)
            {
                var shortcut = ParseShortcutFile(file);
                if (shortcut != null && shortcut.IsValid)
                {
                    shortcuts.Add(shortcut);
                }
            }
            
            // 也扫描可执行文件
            var exeFiles = Directory.GetFiles(folderPath, "*.exe", SearchOption.TopDirectoryOnly);
            foreach (var file in exeFiles)
            {
                var shortcut = CreateShortcutFromExe(file);
                if (shortcut != null)
                {
                    shortcuts.Add(shortcut);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"扫描文件夹失败 {folderPath}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 解析快捷方式文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>快捷方式信息</returns>
    private ShortcutInfo? ParseShortcutFile(string filePath)
    {
        try
        {
            var fileInfo = new FileInfo(filePath);
            var name = Path.GetFileNameWithoutExtension(filePath);
            
            // 简化版本：直接使用文件信息
            var shortcut = new ShortcutInfo
            {
                Name = name,
                ShortcutPath = filePath,
                TargetPath = filePath, // 简化处理
                Type = ShortcutType.Desktop,
                CreatedTime = fileInfo.CreationTime,
                LastAccessTime = fileInfo.LastAccessTime,
                IsValid = true,
                Category = DetermineCategory(name),
            };
            
            return shortcut;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"解析快捷方式失败 {filePath}: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// 从可执行文件创建快捷方式信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>快捷方式信息</returns>
    private ShortcutInfo? CreateShortcutFromExe(string filePath)
    {
        try
        {
            var fileInfo = new FileInfo(filePath);
            var name = Path.GetFileNameWithoutExtension(filePath);
            
            var shortcut = new ShortcutInfo
            {
                Name = name,
                ShortcutPath = filePath,
                TargetPath = filePath,
                Type = ShortcutType.Desktop,
                CreatedTime = fileInfo.CreationTime,
                LastAccessTime = fileInfo.LastAccessTime,
                IsValid = File.Exists(filePath),
                Category = DetermineCategory(name),
            };
            
            return shortcut;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"创建快捷方式信息失败 {filePath}: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// 确定应用分类
    /// </summary>
    /// <param name="name">应用名称</param>
    /// <returns>分类</returns>
    private string DetermineCategory(string name)
    {
        var lowerName = name.ToLower();
        
        // PDF相关
        if (lowerName.Contains("pdf") || lowerName.Contains("acrobat") || 
            lowerName.Contains("foxit") || lowerName.Contains("reader"))
            return "PDF";
            
        // 创作工具
        if (lowerName.Contains("photoshop") || lowerName.Contains("illustrator") ||
            lowerName.Contains("premiere") || lowerName.Contains("after") ||
            lowerName.Contains("blender") || lowerName.Contains("maya") ||
            lowerName.Contains("3ds") || lowerName.Contains("cinema"))
            return "Creative";
            
        // 开发工具
        if (lowerName.Contains("visual") || lowerName.Contains("code") ||
            lowerName.Contains("studio") || lowerName.Contains("intellij") ||
            lowerName.Contains("eclipse") || lowerName.Contains("git") ||
            lowerName.Contains("node") || lowerName.Contains("python"))
            return "Development";
            
        // 系统工具
        if (lowerName.Contains("windows") || lowerName.Contains("system") ||
            lowerName.Contains("control") || lowerName.Contains("registry") ||
            lowerName.Contains("task") || lowerName.Contains("service"))
            return "System";
            
        // 游戏
        if (lowerName.Contains("game") || lowerName.Contains("steam") ||
            lowerName.Contains("origin") || lowerName.Contains("epic") ||
            lowerName.Contains("uplay") || lowerName.Contains("battle"))
            return "Game";

        // 浏览器和网络工具
        if (lowerName.Contains("chrome") || lowerName.Contains("firefox") ||
            lowerName.Contains("edge") || lowerName.Contains("browser") ||
            lowerName.Contains("safari") || lowerName.Contains("opera"))
            return "PDF"; // 暂时归类到 PDF 相关，因为界面上有这个分类

        // 向日葵等远程工具
        if (lowerName.Contains("向日葵") || lowerName.Contains("sunlogin") ||
            lowerName.Contains("teamviewer") || lowerName.Contains("remote"))
            return "System"; // 归类到系统工具

        return "System"; // 默认归类到系统工具，确保有分类按钮可以显示
    }
    
    /// <summary>
    /// 启动应用程序
    /// </summary>
    /// <param name="shortcut">快捷方式信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> LaunchApplicationAsync(ShortcutInfo shortcut)
    {
        try
        {
            if (string.IsNullOrEmpty(shortcut.TargetPath) || !File.Exists(shortcut.TargetPath))
            {
                Debug.WriteLine($"目标文件不存在: {shortcut.TargetPath}");
                return false;
            }
            
            var startInfo = new ProcessStartInfo
            {
                FileName = shortcut.TargetPath,
                UseShellExecute = true,
                WorkingDirectory = shortcut.WorkingDirectory
            };
            
            if (!string.IsNullOrEmpty(shortcut.Arguments))
            {
                startInfo.Arguments = shortcut.Arguments;
            }
            
            await Task.Run(() => Process.Start(startInfo));
            
            // 更新最后访问时间
            shortcut.LastAccessTime = DateTime.Now;
            
            Debug.WriteLine($"成功启动应用: {shortcut.Name}");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"启动应用失败 {shortcut.Name}: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 添加快捷方式
    /// </summary>
    /// <param name="targetPath">目标路径</param>
    /// <param name="name">快捷方式名称</param>
    /// <returns>是否成功</returns>
    public async Task<bool> AddShortcutAsync(string targetPath, string name = "")
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                name = Path.GetFileNameWithoutExtension(targetPath);
            }

            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            var shortcutPath = Path.Combine(desktopPath, $"{name}.lnk");

            // 简化版本：直接复制文件或创建快捷方式
            // 实际实现需要使用 Windows Shell API
            await Task.Delay(100); // 模拟操作

            Debug.WriteLine($"添加快捷方式: {name} -> {targetPath}");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"添加快捷方式失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 从拖入的文件创建快捷方式信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>快捷方式信息</returns>
    public async Task<ShortcutInfo?> CreateShortcutFromDroppedFileAsync(string filePath)
    {
        try
        {
            Debug.WriteLine($"开始处理拖拽文件: {filePath}");

            if (!File.Exists(filePath))
            {
                Debug.WriteLine($"文件不存在: {filePath}");
                return null;
            }

            var extension = Path.GetExtension(filePath).ToLower();
            var name = Path.GetFileNameWithoutExtension(filePath);
            string targetPath = filePath;
            string workingDirectory = Path.GetDirectoryName(filePath) ?? "";

            Debug.WriteLine($"文件扩展名: {extension}");

            // 如果是快捷方式文件，需要解析目标路径
            if (extension == ".lnk")
            {
                Debug.WriteLine("检测到快捷方式文件，开始解析...");
                var linkInfo = await ResolveLinkFileAsync(filePath);
                if (linkInfo != null)
                {
                    targetPath = linkInfo.TargetPath ?? "";
                    workingDirectory = linkInfo.WorkingDirectory ?? "";
                    Debug.WriteLine($"解析快捷方式成功: 目标={targetPath}, 工作目录={workingDirectory}");
                }
                else
                {
                    Debug.WriteLine("解析快捷方式失败");
                    return null;
                }
            }

            // 提取应用程序图标
            var iconPath = await ExtractApplicationIconAsync(targetPath);
            Debug.WriteLine($"图标提取结果: {iconPath ?? "无"}");

            // 创建快捷方式信息
            var shortcut = new ShortcutInfo
            {
                Name = name ?? string.Empty,
                TargetPath = targetPath ?? string.Empty,
                ShortcutPath = filePath ?? string.Empty,
                WorkingDirectory = workingDirectory ?? string.Empty,
                Type = ShortcutType.Desktop,
                CreatedTime = DateTime.Now,
                LastAccessTime = DateTime.Now,
                IsValid = true,
                Category = DetermineCategory(name ?? string.Empty) ?? string.Empty,
                Description = $"拖入的应用程序: {name ?? string.Empty}",
                IconPath = iconPath ?? string.Empty
            };

            Debug.WriteLine($"创建快捷方式成功: {name} ({shortcut.Category})");
            return await Task.FromResult(shortcut);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"创建快捷方式失败: {ex.Message}");
            Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            return null;
        }
    }

    /// <summary>
    /// 解析快捷方式文件
    /// </summary>
    /// <param name="linkPath">快捷方式文件路径</param>
    /// <returns>链接信息</returns>
    private async Task<LinkInfo?> ResolveLinkFileAsync(string linkPath)
    {
        try
        {
            return await Task.Run(() =>
            {
                try
                {
#if WINDOWS
                    var type = Type.GetTypeFromProgID("WScript.Shell");
                    if (type == null)
                    {
                        Debug.WriteLine("无法获取WScript.Shell类型，当前平台可能不支持。");
                        return null;
                    }
                    dynamic? shell = Activator.CreateInstance(type);
                    if (shell == null)
                    {
                        Debug.WriteLine("无法创建WScript.Shell实例。");
                        return null;
                    }
                    dynamic? shortcut = shell.CreateShortcut(linkPath);
                    if (shortcut == null)
                    {
                        Debug.WriteLine("无法创建快捷方式对象。");
                        return null;
                    }
                    var targetPath = shortcut.TargetPath as string;
                    var workingDirectory = shortcut.WorkingDirectory as string;
                    var arguments = shortcut.Arguments as string;

                    Debug.WriteLine($"快捷方式解析结果: 目标={targetPath}, 工作目录={workingDirectory}, 参数={arguments}");

                    if (!string.IsNullOrEmpty(targetPath))
                    {
                        return new LinkInfo
                        {
                            TargetPath = targetPath ?? string.Empty,
                            WorkingDirectory = workingDirectory ?? Path.GetDirectoryName(targetPath) ?? string.Empty,
                            Arguments = arguments ?? string.Empty
                        };
                    }
#endif
                    return null;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"使用 WScript.Shell 解析快捷方式失败: {ex.Message}");

                    // 备用方法：尝试简单的文件读取解析
                    try
                    {
                        var bytes = File.ReadAllBytes(linkPath);
                        var targetPath = ExtractTargetFromLinkBytes(bytes);
                        if (!string.IsNullOrEmpty(targetPath))
                        {
                            return new LinkInfo
                            {
                                TargetPath = targetPath ?? string.Empty,
                                WorkingDirectory = Path.GetDirectoryName(targetPath) ?? string.Empty,
                                Arguments = string.Empty
                            };
                        }
                    }
                    catch (Exception ex2)
                    {
                        Debug.WriteLine($"备用解析方法也失败: {ex2.Message}");
                    }

                    return null;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"解析快捷方式文件失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 从 .lnk 文件字节中提取目标路径（简化版本）
    /// </summary>
    private string? ExtractTargetFromLinkBytes(byte[] bytes)
    {
        try
        {
            // 这是一个非常简化的 .lnk 文件解析
            // 实际的 .lnk 文件格式很复杂，这里只是尝试找到可能的路径
            var content = System.Text.Encoding.Unicode.GetString(bytes);

            // 查找常见的可执行文件路径模式
            var patterns = new[]
            {
                @"[A-Z]:\\[^\\/:*?""<>|]*\.exe",
                @"[A-Z]:\\Program Files[^\\/:*?""<>|]*\.exe",
                @"[A-Z]:\\Program Files \(x86\)[^\\/:*?""<>|]*\.exe"
            };

            foreach (var pattern in patterns)
            {
                var matches = System.Text.RegularExpressions.Regex.Matches(content, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                foreach (System.Text.RegularExpressions.Match match in matches)
                {
                    var path = match.Value;
                    if (File.Exists(path))
                    {
                        Debug.WriteLine($"通过字节解析找到目标路径: {path}");
                        return path;
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"字节解析失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 提取应用程序图标
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>图标文件路径</returns>
    private async Task<string?> ExtractApplicationIconAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            var extension = Path.GetExtension(filePath).ToLower();

            // 对于 .exe 文件，提取内嵌图标
            if (extension == ".exe")
            {
                return await ExtractExeIconAsync(filePath);
            }
            // 对于 .lnk 文件，获取目标文件的图标
            else if (extension == ".lnk")
            {
                // 简化处理，直接返回 null，使用默认图标
                return null;
            }

            return null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"提取图标失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 提取 EXE 文件的图标
    /// </summary>
    /// <param name="exePath">EXE 文件路径</param>
    /// <returns>图标文件路径</returns>
    private async Task<string?> ExtractExeIconAsync(string exePath)
    {
#if WINDOWS
        try
        {
            var iconCacheDir = Path.Combine(Path.GetTempPath(), "LSSOFT", "Icons");
            Directory.CreateDirectory(iconCacheDir);
            var fileName = Path.GetFileNameWithoutExtension(exePath);
            var iconPath = Path.Combine(iconCacheDir, $"{fileName}.png");
            if (File.Exists(iconPath))
            {
                return iconPath;
            }
            return await Task.Run(() =>
            {
                try
                {
                    Debug.WriteLine($"开始提取图标: {exePath}");
                    var icon = Icon.ExtractAssociatedIcon(exePath);
                    if (icon != null)
                    {
                        Debug.WriteLine($"成功提取图标对象，尺寸: {icon.Width}x{icon.Height}");
                        using (var bitmap = icon.ToBitmap())
                        {
                            Debug.WriteLine($"转换为 Bitmap，尺寸: {bitmap.Width}x{bitmap.Height}");
                            bitmap.Save(iconPath, System.Drawing.Imaging.ImageFormat.Png);
                            Debug.WriteLine($"图标保存成功: {iconPath}");
                        }
                        if (File.Exists(iconPath))
                        {
                            var fileInfo = new FileInfo(iconPath);
                            Debug.WriteLine($"图标文件验证成功: {iconPath}, 大小: {fileInfo.Length} bytes");
                            return iconPath;
                        }
                        else
                        {
                            Debug.WriteLine($"图标文件保存失败: {iconPath}");
                            return null;
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"无法提取图标: {exePath}");
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"提取 EXE 图标失败: {ex.Message}");
                    Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    return null;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"提取图标过程失败: {ex.Message}");
            return null;
        }
#else
        await Task.CompletedTask;
        return null;
#endif
    }
    
    /// <summary>
    /// 删除快捷方式
    /// </summary>
    /// <param name="shortcut">快捷方式信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteShortcutAsync(ShortcutInfo shortcut)
    {
        try
        {
            if (File.Exists(shortcut.ShortcutPath))
            {
                await Task.Run(() => File.Delete(shortcut.ShortcutPath));
                Debug.WriteLine($"删除快捷方式: {shortcut.Name}");
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"删除快捷方式失败 {shortcut.Name}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 添加示例快捷方式数据
    /// </summary>
    /// <param name="shortcuts">快捷方式列表</param>
    private void AddSampleShortcuts(List<ShortcutInfo> shortcuts)
    {
        var sampleApps = new[]
        {
            new { Name = "Chrome", Category = "PDF", Target = "chrome.exe" },
            new { Name = "Adobe Reader", Category = "PDF", Target = "acrord32.exe" },
            new { Name = "Foxit Reader", Category = "PDF", Target = "foxitreader.exe" },
            new { Name = "PDF24", Category = "PDF", Target = "pdf24.exe" },
            new { Name = "Photoshop", Category = "Creative", Target = "photoshop.exe" },
            new { Name = "Illustrator", Category = "Creative", Target = "illustrator.exe" },
            new { Name = "Premiere Pro", Category = "Creative", Target = "premiere.exe" },
            new { Name = "After Effects", Category = "Creative", Target = "afterfx.exe" },
            new { Name = "Blender", Category = "Creative", Target = "blender.exe" },
            new { Name = "Visual Studio", Category = "Development", Target = "devenv.exe" },
            new { Name = "VS Code", Category = "Development", Target = "code.exe" },
            new { Name = "IntelliJ IDEA", Category = "Development", Target = "idea.exe" },
            new { Name = "Git", Category = "Development", Target = "git.exe" },
            new { Name = "Node.js", Category = "Development", Target = "node.exe" },
            new { Name = "Task Manager", Category = "System", Target = "taskmgr.exe" },
            new { Name = "Control Panel", Category = "System", Target = "control.exe" },
            new { Name = "Registry Editor", Category = "System", Target = "regedit.exe" },
            new { Name = "Command Prompt", Category = "System", Target = "cmd.exe" },
            new { Name = "Steam", Category = "Game", Target = "steam.exe" },
            new { Name = "Epic Games", Category = "Game", Target = "epicgames.exe" },
            new { Name = "Origin", Category = "Game", Target = "origin.exe" },
            new { Name = "Battle.net", Category = "Game", Target = "battlenet.exe" }
        };

        foreach (var app in sampleApps)
        {
            shortcuts.Add(new ShortcutInfo
            {
                Name = app.Name,
                TargetPath = app.Target,
                ShortcutPath = $"{app.Name}.lnk",
                Type = ShortcutType.Desktop,
                Category = app.Category,
                IsValid = true,
                CreatedTime = DateTime.Now.AddDays(-Random.Shared.Next(1, 30)),
                LastAccessTime = DateTime.Now.AddHours(-Random.Shared.Next(1, 24))
            });
        }

        Debug.WriteLine($"添加了 {sampleApps.Length} 个示例应用");
    }
}

/// <summary>
/// 链接信息辅助类
/// </summary>
internal class LinkInfo
{
    public string TargetPath { get; set; } = string.Empty;
    public string WorkingDirectory { get; set; } = string.Empty;
    public string Arguments { get; set; } = string.Empty;
}
