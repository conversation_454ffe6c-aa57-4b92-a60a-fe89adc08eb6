#if WINDOWS
using Microsoft.Win32;
#endif
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace LSSOFT.Services
{
    public class InstalledSoftware
    {
        public string Name { get; set; } = "";
        public string Version { get; set; } = "";
        public string Publisher { get; set; } = "";
        public string InstallDate { get; set; } = "";
        public string InstallLocation { get; set; } = "";
        public string UninstallString { get; set; } = "";
        public long EstimatedSize { get; set; } = 0;
        public string DisplayIcon { get; set; } = "";
    }

#if WINDOWS
    public class WindowsSoftwareScanner
    {
        private readonly List<string> _registryPaths = new()
        {
            @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        };

        public async Task<List<InstalledSoftware>> ScanInstalledSoftwareAsync()
        {
            return await Task.Run(() =>
            {
                var softwareList = new List<InstalledSoftware>();
                
                try
                {
                    // 扫描HKEY_LOCAL_MACHINE
                    foreach (var path in _registryPaths)
                    {
                        ScanRegistryKey(Registry.LocalMachine, path, softwareList);
                    }

                    // 扫描HKEY_CURRENT_USER
                    foreach (var path in _registryPaths)
                    {
                        ScanRegistryKey(Registry.CurrentUser, path, softwareList);
                    }

                    // 过滤和清理数据
                    var filteredList = softwareList
                        .Where(s => !string.IsNullOrWhiteSpace(s.Name))
                        .Where(s => !IsSystemComponent(s))
                        .GroupBy(s => s.Name.ToLower())
                        .Select(g => g.First())
                        .OrderBy(s => s.Name)
                        .ToList();

                    Debug.WriteLine($"扫描完成，找到 {filteredList.Count} 个软件");
                    return filteredList;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"扫描软件时出错: {ex.Message}");
                    return new List<InstalledSoftware>();
                }
            });
        }

        private void ScanRegistryKey(RegistryKey rootKey, string path, List<InstalledSoftware> softwareList)
        {
            try
            {
                using var key = rootKey.OpenSubKey(path);
                if (key == null) return;

                foreach (var subKeyName in key.GetSubKeyNames())
                {
                    try
                    {
                        using var subKey = key.OpenSubKey(subKeyName);
                        if (subKey == null) continue;

                        var software = ExtractSoftwareInfo(subKey);
                        if (software != null)
                        {
                            softwareList.Add(software);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"读取注册表项 {subKeyName} 时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"打开注册表路径 {path} 时出错: {ex.Message}");
            }
        }

        private InstalledSoftware? ExtractSoftwareInfo(RegistryKey key)
        {
            try
            {
                var displayName = key.GetValue("DisplayName")?.ToString();
                if (string.IsNullOrWhiteSpace(displayName))
                    return null;

                // 跳过系统更新和补丁
                if (displayName.Contains("Update for") ||
                    displayName.Contains("Hotfix for") ||
                    displayName.StartsWith("Security Update") ||
                    displayName.StartsWith("KB"))
                    return null;

                // 清理软件名称，处理可能的编码问题
                var cleanName = CleanSoftwareName(displayName);
                if (string.IsNullOrWhiteSpace(cleanName))
                    return null;

                var software = new InstalledSoftware
                {
                    Name = cleanName,
                    Version = key.GetValue("DisplayVersion")?.ToString() ?? "未知",
                    Publisher = key.GetValue("Publisher")?.ToString() ?? "未知",
                    InstallDate = FormatInstallDate(key.GetValue("InstallDate")?.ToString()),
                    InstallLocation = key.GetValue("InstallLocation")?.ToString() ?? "",
                    UninstallString = key.GetValue("UninstallString")?.ToString() ?? "",
                    DisplayIcon = key.GetValue("DisplayIcon")?.ToString() ?? ""
                };

                // 尝试获取大小信息
                if (key.GetValue("EstimatedSize") is int size)
                {
                    software.EstimatedSize = size * 1024L; // 转换为字节
                }

                return software;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取软件信息时出错: {ex.Message}");
                return null;
            }
        }

        private string CleanSoftwareName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return "";

            // 移除可能的乱码字符
            var cleanName = name.Trim();

            // 如果包含大量特殊字符，可能是乱码，跳过
            var specialCharCount = cleanName.Count(c => !char.IsLetterOrDigit(c) && !char.IsWhiteSpace(c) && c != '.' && c != '-' && c != '_' && c != '(' && c != ')');
            if (specialCharCount > cleanName.Length * 0.3)
            {
                return "";
            }

            return cleanName;
        }

        private string FormatInstallDate(string? installDate)
        {
            if (string.IsNullOrWhiteSpace(installDate) || installDate.Length != 8)
                return "未知";

            try
            {
                var year = installDate.Substring(0, 4);
                var month = installDate.Substring(4, 2);
                var day = installDate.Substring(6, 2);
                return $"{year}-{month}-{day}";
            }
            catch
            {
                return "未知";
            }
        }

        private bool IsSystemComponent(InstalledSoftware software)
        {
            var systemKeywords = new[]
            {
                "Microsoft Visual C++",
                "Microsoft .NET",
                "Windows SDK",
                "Microsoft Office Click-to-Run",
                "Microsoft OneDrive",
                "Windows Driver Package",
                "Windows Software Development Kit",
                "Microsoft Windows SDK",
                "Windows Mobile Extension SDK",
                "Windows IoT Extension SDK",
                "Microsoft Edge WebView2",
                "Microsoft Edge Update",
                "Windows App Runtime",
                "Microsoft Visual Studio",
                "Microsoft Build Tools",
                "Windows Subsystem for Linux",
                "Microsoft SQL Server",
                "Windows Feature Experience Pack"
            };

            var name = software.Name.ToLower();

            // 检查是否包含系统关键词
            if (systemKeywords.Any(keyword =>
                software.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
                return true;

            // 检查是否是Windows更新或补丁
            if (name.Contains("update") || name.Contains("hotfix") ||
                name.Contains("security update") || name.StartsWith("kb"))
                return true;

            // 检查是否是驱动程序
            if (name.Contains("driver") && name.Contains("package"))
                return true;

            return false;
        }

        public string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "未知";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        public string GetSoftwareIcon(InstalledSoftware software)
        {
            // 根据软件名称返回合适的图标
            var name = software.Name.ToLower();
            
            if (name.Contains("visual studio")) return "💻";
            if (name.Contains("chrome")) return "🌐";
            if (name.Contains("firefox")) return "🦊";
            if (name.Contains("office") || name.Contains("word") || name.Contains("excel")) return "📄";
            if (name.Contains("steam") || name.Contains("game")) return "🎮";
            if (name.Contains("autocad") || name.Contains("solidworks")) return "📐";
            if (name.Contains("photoshop") || name.Contains("illustrator")) return "🎨";
            if (name.Contains("spotify") || name.Contains("music")) return "🎵";
            if (name.Contains("video") || name.Contains("vlc")) return "🎬";
            if (name.Contains("zip") || name.Contains("rar") || name.Contains("7-zip")) return "📦";
            if (name.Contains("antivirus") || name.Contains("security")) return "🛡️";
            if (name.Contains("driver")) return "⚙️";
            if (name.Contains("java")) return "☕";
            if (name.Contains("python")) return "🐍";
            if (name.Contains("node")) return "📗";
            if (name.Contains("git")) return "📚";
            
            return "📦"; // 默认图标
        }
    }
#else
    // 非Windows平台下，定义空实现避免编译错误
    public partial class WindowsSoftwareScanner
    {
        private readonly List<string> _registryPaths = new()
        {
            @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        };

        public Task<List<InstalledSoftware>> ScanInstalledSoftwareAsync()
        {
            return Task.FromResult(new List<InstalledSoftware>());
        }

        private void ScanRegistryKey(object rootKey, string path, List<InstalledSoftware> softwareList) { }

        private InstalledSoftware? ExtractSoftwareInfo(object key) => null;

        private string CleanSoftwareName(string name) => name;

        private string FormatInstallDate(string? installDate) => "未知";

        private bool IsSystemComponent(InstalledSoftware software) => false;

        public string FormatFileSize(long bytes) => "未知";

        public string GetSoftwareIcon(InstalledSoftware software) => "📦";
    }
#endif
}
