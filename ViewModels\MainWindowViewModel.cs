﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Views.Modules;

namespace LSSOFT.ViewModels;

public partial class MainWindowViewModel : ViewModelBase
{
    [ObservableProperty]
    private object? currentView;

    [ObservableProperty]
    private string statusMessage = "欢迎使用个人软件综合管理系统";

    [ObservableProperty]
    private string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

    [ObservableProperty]
    private bool isNavigationExpanded = false;

    [ObservableProperty]
    private GridLength navigationWidth = new(60); // 默认折叠宽度，更紧凑

    // 子菜单展开状态
    [ObservableProperty]
    private bool isSoftwareManagementExpanded = false;

    [ObservableProperty]
    private bool isShortcutManagementExpanded = false;

    [ObservableProperty]
    private bool isSystemCleanupExpanded = false;

    // 箭头旋转角度
    [ObservableProperty]
    private double softwareManagementRotation = 0;

    [ObservableProperty]
    private double shortcutManagementRotation = 0;

    [ObservableProperty]
    private double systemCleanupRotation = 0;

    // 当前激活的菜单项
    [ObservableProperty]
    private string currentActiveMenu = "Dashboard";

    // 激活状态属性 - 使用 ObservableProperty
    [ObservableProperty]
    private bool isHomeActive = true;

    [ObservableProperty]
    private bool isSoftwareManagementActive = false;

    [ObservableProperty]
    private bool isShortcutManagementActive = false;

    [ObservableProperty]
    private bool isSystemCleanupActive = false;

    [ObservableProperty]
    private bool isCrackToolsActive = false;

    [ObservableProperty]
    private bool isAutomationActive = false;

    [ObservableProperty]
    private bool isGameSoftwareActive = false;

    // 子菜单项激活状态
    [ObservableProperty]
    private bool isInstalledSoftwareActive = false;

    [ObservableProperty]
    private bool isSoftwarePackagesActive = false;

    [ObservableProperty]
    private bool isSoftwareUpdatesActive = false;

    [ObservableProperty]
    private bool isDesktopShortcutsActive = false;

    [ObservableProperty]
    private bool isStartMenuActive = false;

    [ObservableProperty]
    private bool isQuickLaunchActive = false;

    [ObservableProperty]
    private bool isSystemCleanupPageActive = false;

    [ObservableProperty]
    private bool isStartupManagerActive = false;

    [ObservableProperty]
    private bool isServiceManagerActive = false;

    // 菜单项列表 - 用于滚轮切换
    private readonly List<string> menuItems = new()
    {
        "Dashboard",
        "InstalledSoftware",
        "DesktopShortcuts",
        "SystemCleanup",
        "CrackTools",
        "AutomationScripts",
        "GameSoftware"
    };

    private int currentMenuIndex = 0;

    public ICommand NavigateCommand { get; }
    public ICommand ToggleNavigationCommand { get; }
    public ICommand ToggleSoftwareManagementCommand { get; }
    public ICommand ToggleShortcutManagementCommand { get; }
    public ICommand ToggleSystemCleanupCommand { get; }
    public ICommand NavigateToHomeCommand { get; }
    public ICommand NavigateToSoftwareManagementCommand { get; }
    public ICommand NavigateToShortcutManagementCommand { get; }
    public ICommand NavigateToSystemCleanupCommand { get; }
    public ICommand ScrollUpCommand { get; }
    public ICommand ScrollDownCommand { get; }

    public MainWindowViewModel()
    {
        NavigateCommand = new RelayCommand<string>(Navigate);
        ToggleNavigationCommand = new RelayCommand(ToggleNavigation);
        ToggleSoftwareManagementCommand = new RelayCommand(ToggleSoftwareManagement);
        ToggleShortcutManagementCommand = new RelayCommand(ToggleShortcutManagement);
        ToggleSystemCleanupCommand = new RelayCommand(ToggleSystemCleanup);
        NavigateToHomeCommand = new RelayCommand(NavigateToHome);
        NavigateToSoftwareManagementCommand = new RelayCommand(NavigateToSoftwareManagement);
        NavigateToShortcutManagementCommand = new RelayCommand(NavigateToShortcutManagement);
        NavigateToSystemCleanupCommand = new RelayCommand(NavigateToSystemCleanup);
        ScrollUpCommand = new RelayCommand(ScrollUp);
        ScrollDownCommand = new RelayCommand(ScrollDown);

        // 设置默认视图
        CurrentView = new DashboardView();

        // 启动时间更新任务
        _ = UpdateTimeAsync();
    }

    private void Navigate(string? viewName)
    {
        if (string.IsNullOrEmpty(viewName))
            return;

        try
        {
            CurrentView = viewName switch
            {
                "Dashboard" => new DashboardView(),
                "InstalledSoftware" => new InstalledSoftwareView(),
                "SoftwarePackages" => new SoftwarePackagesView(),
                "SoftwareUpdates" => new SoftwareLocalizationView(),
                "DesktopShortcuts" => new DesktopShortcutsView(),
                "StartMenu" => new StartMenuView(),
                "QuickLaunch" => new QuickLaunchView(),
                "SystemCleanup" => new SystemCleanupView(),
                "StartupManager" => new StartupManagerView(),
                "ServiceManager" => new ServiceManagerView(),
                "CrackTools" => new CrackToolsView(),
                "LicenseManager" => new LicenseManagerView(),
                "ActivationHistory" => new ActivationHistoryView(),
                "AutomationScripts" => new AutomationScriptsView(),
                "BatchTasks" => new BatchTasksView(),
                "ScheduledTasks" => new ScheduledTasksView(),
                "GameSoftware" => new GameSoftwareView(),
                "EngineeringSoftware" => new EngineeringSoftwareView(),
                "OfficeSoftware" => new OfficeSoftwareView(),
                "DevelopmentTools" => new DevelopmentToolsView(),
                _ => new DashboardView()
            };

            // 更新当前激活的菜单项
            CurrentActiveMenu = viewName;

            // 如果导航到单项页面（非子菜单页面），则折叠所有子菜单
            if (viewName == "Dashboard" || viewName == "CrackTools" ||
                viewName == "AutomationScripts" || viewName == "GameSoftware")
            {
                CollapseAllSubMenus();
            }

            UpdateActiveStates();

            // 更新菜单索引
            var index = menuItems.IndexOf(viewName);
            if (index >= 0)
            {
                currentMenuIndex = index;
            }

            StatusMessage = $"已切换到: {GetViewDisplayName(viewName)}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"导航失败: {ex.Message}";
            CurrentView = new DashboardView();
            CurrentActiveMenu = "Dashboard";
        }
    }

    private static string GetViewDisplayName(string viewName)
    {
        return viewName switch
        {
            "Dashboard" => "主页",
            "InstalledSoftware" => "已安装软件",
            "SoftwarePackages" => "软件安装包",
            "SoftwareUpdates" => "软件更新",
            "DesktopShortcuts" => "桌面快捷方式",
            "StartMenu" => "开始菜单",
            "QuickLaunch" => "快速启动栏",
            "SystemCleanup" => "系统清理",
            "StartupManager" => "启动项管理",
            "ServiceManager" => "服务管理",
            "CrackTools" => "破解工具",
            "LicenseManager" => "许可证管理",
            "ActivationHistory" => "激活记录",
            "AutomationScripts" => "自动化脚本",
            "BatchTasks" => "批处理任务",
            "ScheduledTasks" => "定时任务",
            "GameSoftware" => "游戏软件",
            "EngineeringSoftware" => "工程软件",
            "OfficeSoftware" => "办公软件",
            "DevelopmentTools" => "开发工具",
            _ => "仪表板"
        };
    }

    private void ToggleNavigation()
    {
        IsNavigationExpanded = !IsNavigationExpanded;
        NavigationWidth = IsNavigationExpanded ? new GridLength(240) : new GridLength(60); // 折叠60px，展开240px
        StatusMessage = IsNavigationExpanded ? "导航栏已展开" : "导航栏已折叠";
    }

    private void ToggleSoftwareManagement()
    {
        IsSoftwareManagementExpanded = !IsSoftwareManagementExpanded;
        SoftwareManagementRotation = IsSoftwareManagementExpanded ? 90 : 0;
    }

    private void ToggleShortcutManagement()
    {
        IsShortcutManagementExpanded = !IsShortcutManagementExpanded;
        ShortcutManagementRotation = IsShortcutManagementExpanded ? 90 : 0;
    }

    private void ToggleSystemCleanup()
    {
        IsSystemCleanupExpanded = !IsSystemCleanupExpanded;
        SystemCleanupRotation = IsSystemCleanupExpanded ? 90 : 0;
    }

    // 新的导航方法 - 用于折叠状态下的直接导航
    private void NavigateToHome()
    {
        // 折叠所有子菜单
        CollapseAllSubMenus();
        Navigate("Dashboard");
    }

    private void NavigateToSoftwareManagement()
    {
        if (IsNavigationExpanded)
        {
            // 展开状态：如果子菜单已展开，则折叠；如果未展开，则展开
            if (IsSoftwareManagementExpanded)
            {
                // 子菜单已展开，点击后折叠
                IsSoftwareManagementExpanded = false;
                SoftwareManagementRotation = 0;
            }
            else
            {
                // 先折叠其他子菜单
                IsShortcutManagementExpanded = false;
                ShortcutManagementRotation = 0;
                IsSystemCleanupExpanded = false;
                SystemCleanupRotation = 0;

                // 然后展开当前子菜单
                IsSoftwareManagementExpanded = true;
                SoftwareManagementRotation = 90;
            }
        }
        else
        {
            // 折叠状态：直接导航到第一个子项
            Navigate("InstalledSoftware");
        }
    }

    private void NavigateToShortcutManagement()
    {
        if (IsNavigationExpanded)
        {
            // 展开状态：如果子菜单已展开，则折叠；如果未展开，则展开
            if (IsShortcutManagementExpanded)
            {
                // 子菜单已展开，点击后折叠
                IsShortcutManagementExpanded = false;
                ShortcutManagementRotation = 0;
            }
            else
            {
                // 先折叠其他子菜单
                IsSoftwareManagementExpanded = false;
                SoftwareManagementRotation = 0;
                IsSystemCleanupExpanded = false;
                SystemCleanupRotation = 0;

                // 然后展开当前子菜单
                IsShortcutManagementExpanded = true;
                ShortcutManagementRotation = 90;
            }
        }
        else
        {
            // 折叠状态：直接导航到第一个子项
            Navigate("DesktopShortcuts");
        }
    }

    private void NavigateToSystemCleanup()
    {
        if (IsNavigationExpanded)
        {
            // 展开状态：如果子菜单已展开，则折叠；如果未展开，则展开
            if (IsSystemCleanupExpanded)
            {
                // 子菜单已展开，点击后折叠
                IsSystemCleanupExpanded = false;
                SystemCleanupRotation = 0;
            }
            else
            {
                // 先折叠其他子菜单
                IsSoftwareManagementExpanded = false;
                SoftwareManagementRotation = 0;
                IsShortcutManagementExpanded = false;
                ShortcutManagementRotation = 0;

                // 然后展开当前子菜单
                IsSystemCleanupExpanded = true;
                SystemCleanupRotation = 90;
            }
        }
        else
        {
            // 折叠状态：直接导航到第一个子项
            Navigate("SystemCleanup");
        }
    }

    // 滚轮向上切换菜单
    private void ScrollUp()
    {
        if (currentMenuIndex > 0)
        {
            currentMenuIndex--;
            Navigate(menuItems[currentMenuIndex]);
        }
    }

    // 滚轮向下切换菜单
    private void ScrollDown()
    {
        if (currentMenuIndex < menuItems.Count - 1)
        {
            currentMenuIndex++;
            Navigate(menuItems[currentMenuIndex]);
        }
    }

    // 折叠所有子菜单
    private void CollapseAllSubMenus()
    {
        IsSoftwareManagementExpanded = false;
        SoftwareManagementRotation = 0;

        IsShortcutManagementExpanded = false;
        ShortcutManagementRotation = 0;

        IsSystemCleanupExpanded = false;
        SystemCleanupRotation = 0;
    }

    // 更新激活状态
    private void UpdateActiveStates()
    {
        // 重置所有主菜单状态
        IsHomeActive = false;
        IsSoftwareManagementActive = false;
        IsShortcutManagementActive = false;
        IsSystemCleanupActive = false;
        IsCrackToolsActive = false;
        IsAutomationActive = false;
        IsGameSoftwareActive = false;

        // 重置所有子菜单状态
        IsInstalledSoftwareActive = false;
        IsSoftwarePackagesActive = false;
        IsSoftwareUpdatesActive = false;
        IsDesktopShortcutsActive = false;
        IsStartMenuActive = false;
        IsQuickLaunchActive = false;
        IsSystemCleanupPageActive = false;
        IsStartupManagerActive = false;
        IsServiceManagerActive = false;

        // 根据当前菜单设置激活状态
        switch (CurrentActiveMenu)
        {
            case "Dashboard":
                IsHomeActive = true;
                break;

            // 软件管理相关
            case "InstalledSoftware":
                IsSoftwareManagementActive = true;
                IsInstalledSoftwareActive = true;
                break;
            case "SoftwarePackages":
                IsSoftwareManagementActive = true;
                IsSoftwarePackagesActive = true;
                break;
            case "SoftwareUpdates":
                IsSoftwareManagementActive = true;
                IsSoftwareUpdatesActive = true;
                break;

            // 快捷方式管理相关
            case "DesktopShortcuts":
                IsShortcutManagementActive = true;
                IsDesktopShortcutsActive = true;
                break;
            case "StartMenu":
                IsShortcutManagementActive = true;
                IsStartMenuActive = true;
                break;
            case "QuickLaunch":
                IsShortcutManagementActive = true;
                IsQuickLaunchActive = true;
                break;

            // 系统精简相关
            case "SystemCleanup":
                IsSystemCleanupActive = true;
                IsSystemCleanupPageActive = true;
                break;
            case "StartupManager":
                IsSystemCleanupActive = true;
                IsStartupManagerActive = true;
                break;
            case "ServiceManager":
                IsSystemCleanupActive = true;
                IsServiceManagerActive = true;
                break;

            // 其他单项菜单
            case "CrackTools":
                IsCrackToolsActive = true;
                break;
            case "AutomationScripts":
                IsAutomationActive = true;
                break;
            case "GameSoftware":
                IsGameSoftwareActive = true;
                break;
        }
    }

    private async Task UpdateTimeAsync()
    {
        while (true)
        {
            CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            await Task.Delay(1000);
        }
    }
}
