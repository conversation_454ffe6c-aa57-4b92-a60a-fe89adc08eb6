using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Models;
using LSSOFT.Services;

namespace LSSOFT.ViewModels;

/// <summary>
/// 桌面快捷方式视图模型
/// </summary>
public partial class DesktopShortcutsViewModel : ViewModelBase
{
    private readonly ShortcutService _shortcutService;
    
    [ObservableProperty]
    private ObservableCollection<ShortcutDisplayItem> shortcuts = new();
    
    [ObservableProperty]
    private ObservableCollection<ShortcutDisplayItem> filteredShortcuts = new();
    
    [ObservableProperty]
    private string searchText = string.Empty;
    
    [ObservableProperty]
    private string selectedCategory = "全部";

    [ObservableProperty]
    private bool isLoading = false;

    [ObservableProperty]
    private string statusText = "就绪";

    [ObservableProperty]
    private string newCategoryName = "";

    /// <summary>
    /// 自定义分类列表
    /// </summary>
    public ObservableCollection<CategoryItem> Categories { get; } = new();

    /// <summary>
    /// 是否显示添加分类对话框
    /// </summary>
    [ObservableProperty]
    private bool isAddCategoryDialogVisible = false;
    
    /// <summary>
    /// 是否为空状态
    /// </summary>
    public bool IsEmpty => !IsLoading && FilteredShortcuts.Count == 0;
    
    /// <summary>
    /// 是否有项目
    /// </summary>
    public bool HasItems => !IsLoading && FilteredShortcuts.Count > 0;
    

    
    public DesktopShortcutsViewModel()
    {
        _shortcutService = new ShortcutService();

        // 初始化分类列表
        InitializeCategories();

        // 监听搜索文本变化
        PropertyChanged += (sender, e) =>
        {
            if (e.PropertyName == nameof(SearchText) || e.PropertyName == nameof(SelectedCategory))
            {
                FilterShortcuts();
            }
        };

        // 不自动加载，等待用户拖入应用
        StatusText = "请拖拽应用程序到此处添加快捷方式";

        // 确保初始状态正确
        OnPropertyChanged(nameof(IsEmpty));
        OnPropertyChanged(nameof(HasItems));
    }

    /// <summary>
    /// 初始化分类列表
    /// </summary>
    private void InitializeCategories()
    {
        Categories.Clear();
        Categories.Add(new CategoryItem("全部") { IsSelected = true });
    }
    
    /// <summary>
    /// 加载快捷方式列表（此方法已禁用自动加载，仅保留用于手动调用）
    /// </summary>
    [RelayCommand]
    public async Task LoadShortcutsAsync()
    {
        try
        {
            // 此方法不再自动加载桌面图标
            // 所有图标都通过拖拽添加
            StatusText = "请拖拽应用程序到此处添加快捷方式";
            await Task.Delay(100);

            // 确保状态正确
            OnPropertyChanged(nameof(IsEmpty));
            OnPropertyChanged(nameof(HasItems));
        }
        catch (Exception ex)
        {
            StatusText = $"操作失败: {ex.Message}";
        }
    }
    
    /// <summary>
    /// 刷新快捷方式列表（仅刷新当前已添加的应用）
    /// </summary>
    [RelayCommand]
    public async Task RefreshAsync()
    {
        try
        {
            // 只刷新筛选，不重新加载桌面图标
            FilterShortcuts();
            StatusText = $"已刷新，当前有 {Shortcuts.Count} 个应用";
            await Task.Delay(100); // 模拟刷新操作
        }
        catch (Exception ex)
        {
            StatusText = $"刷新失败: {ex.Message}";
        }
    }
    
    /// <summary>
    /// 按分类筛选
    /// </summary>
    /// <param name="category">分类</param>
    [RelayCommand]
    public void FilterByCategory(string category)
    {
        SelectedCategory = category;

        // 更新分类选中状态
        foreach (var cat in Categories)
        {
            cat.IsSelected = cat.Name == category;
        }

        FilterShortcuts();
    }
    
    /// <summary>
    /// 筛选快捷方式
    /// </summary>
    private void FilterShortcuts()
    {
        Debug.WriteLine($"开始筛选快捷方式: SelectedCategory={SelectedCategory}, SearchText='{SearchText}'");
        Debug.WriteLine($"原始集合数量: {Shortcuts.Count}");

        var filtered = Shortcuts.AsEnumerable();

        // 按分类筛选
        if (SelectedCategory != "全部")
        {
            // 将中文分类名映射到英文分类名
            var englishCategory = MapChineseCategoryToEnglish(SelectedCategory);
            filtered = filtered.Where(s => s.Category == englishCategory);
            Debug.WriteLine($"按分类 '{SelectedCategory}' (映射为 '{englishCategory}') 筛选后数量: {filtered.Count()}");
        }
        else
        {
            Debug.WriteLine("选择了 '全部' 分类，不进行分类筛选");
        }

        // 按搜索文本筛选
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            var searchLower = SearchText.ToLower();
            filtered = filtered.Where(s =>
                s.Name.ToLower().Contains(searchLower) ||
                s.DisplayName.ToLower().Contains(searchLower));
            Debug.WriteLine($"按搜索文本 '{SearchText}' 筛选后数量: {filtered.Count()}");
        }

        // 强制在 UI 线程更新集合
        Dispatcher.UIThread.Post(() =>
        {
            FilteredShortcuts.Clear();
            var filteredList = filtered.ToList();
            Debug.WriteLine($"最终筛选结果数量: {filteredList.Count}");

            foreach (var item in filteredList)
            {
                FilteredShortcuts.Add(item);
                Debug.WriteLine($"  添加到筛选集合: {item.Name} (分类: {item.Category})");
            }

            Debug.WriteLine($"FilteredShortcuts.Count 更新后: {FilteredShortcuts.Count}");

            // 强制更新 UI 状态
            OnPropertyChanged(nameof(IsEmpty));
            OnPropertyChanged(nameof(HasItems));
            OnPropertyChanged(nameof(FilteredShortcuts));

            Debug.WriteLine($"UI 状态更新完成: IsEmpty={IsEmpty}, HasItems={HasItems}");
        });
    }
    
    /// <summary>
    /// 启动应用程序
    /// </summary>
    /// <param name="shortcut">快捷方式</param>
    [RelayCommand]
    public async Task LaunchApplicationAsync(ShortcutDisplayItem shortcut)
    {
        if (shortcut?.ShortcutInfo == null) return;
        
        try
        {
            StatusText = $"正在启动 {shortcut.DisplayName}...";
            var success = await _shortcutService.LaunchApplicationAsync(shortcut.ShortcutInfo);
            
            StatusText = success ? 
                $"{shortcut.DisplayName} 启动成功" : 
                $"{shortcut.DisplayName} 启动失败";
        }
        catch (Exception ex)
        {
            StatusText = $"启动失败: {ex.Message}";
        }
    }
    
    /// <summary>
    /// 添加快捷方式
    /// </summary>
    [RelayCommand]
    public async Task AddShortcutAsync()
    {
        try
        {
            // 这里应该打开文件选择对话框
            // 简化实现
            StatusText = "请拖拽应用程序到此处添加快捷方式";
            await Task.Delay(100);
        }
        catch (Exception ex)
        {
            StatusText = $"添加失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 处理拖入的文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    public async Task HandleDroppedFileAsync(string filePath)
    {
        try
        {
            Debug.WriteLine($"ViewModel 开始处理拖拽文件: {filePath}");
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            StatusText = $"正在处理 {fileName}...";
            IsLoading = true;

            // 在后台线程处理文件
            var shortcutInfo = await _shortcutService.CreateShortcutFromDroppedFileAsync(filePath);
            Debug.WriteLine($"ShortcutService 返回结果: {(shortcutInfo != null ? "成功" : "失败")}");

            if (shortcutInfo != null)
            {
                Debug.WriteLine($"创建的快捷方式信息: 名称={shortcutInfo.Name}, 路径={shortcutInfo.TargetPath}, 图标={shortcutInfo.IconPath}");

                // 检查是否已存在
                Debug.WriteLine($"检查重复，当前集合中有 {Shortcuts.Count} 个快捷方式");
                foreach (var s in Shortcuts)
                {
                    Debug.WriteLine($"  现有快捷方式: {s.Name}");
                }

                var existing = Shortcuts.FirstOrDefault(s =>
                    s.Name.Equals(shortcutInfo.Name, StringComparison.OrdinalIgnoreCase));
                Debug.WriteLine($"重复检查结果: {(existing != null ? "已存在" : "不存在")}");

                if (existing == null)
                {
                    StatusText = $"正在添加 {fileName}...";

                    // 如果当前选择了自定义分类（不是"全部"），则将应用分配到该分类
                    if (SelectedCategory != "全部")
                    {
                        var englishCategory = MapChineseCategoryToEnglish(SelectedCategory);
                        shortcutInfo.Category = englishCategory;
                        Debug.WriteLine($"将应用分配到当前选择的分类: {SelectedCategory} -> {englishCategory}");
                    }

                    var displayItem = new ShortcutDisplayItem(shortcutInfo);
                    Shortcuts.Add(displayItem);
                    Debug.WriteLine($"快捷方式已添加到集合，当前总数: {Shortcuts.Count}");

                    FilterShortcuts();
                    Debug.WriteLine($"过滤后的快捷方式数量: {FilteredShortcuts.Count}");

                    // 强制更新 UI 状态
                    OnPropertyChanged(nameof(IsEmpty));
                    OnPropertyChanged(nameof(HasItems));
                    Debug.WriteLine($"UI 状态更新: IsEmpty={IsEmpty}, HasItems={HasItems}");

                    // 显示详细的调试信息
                    var iconExists = !string.IsNullOrEmpty(shortcutInfo.IconPath) && File.Exists(shortcutInfo.IconPath);
                    var iconSize = iconExists ? new FileInfo(shortcutInfo.IconPath!).Length : 0;
                    StatusText = $"已添加 {shortcutInfo.Name} - 图标: {(iconExists ? $"成功({iconSize}字节)" : "失败")} - 路径: {shortcutInfo.IconPath ?? "无"}";
                    Debug.WriteLine($"状态文本已更新: {StatusText}");

                    // 额外的调试信息
                    Debug.WriteLine($"=== 集合状态调试 ===");
                    Debug.WriteLine($"Shortcuts.Count: {Shortcuts.Count}");
                    Debug.WriteLine($"FilteredShortcuts.Count: {FilteredShortcuts.Count}");
                    Debug.WriteLine($"SelectedCategory: {SelectedCategory}");
                    Debug.WriteLine($"SearchText: '{SearchText}'");
                    Debug.WriteLine($"IsLoading: {IsLoading}");
                    Debug.WriteLine($"IsEmpty: {IsEmpty}");
                    Debug.WriteLine($"HasItems: {HasItems}");
                    Debug.WriteLine($"新添加的快捷方式分类: {shortcutInfo.Category}");
                    Debug.WriteLine($"=== 调试结束 ===");
                }
                else
                {
                    StatusText = $"{shortcutInfo.Name} 已存在";
                    Debug.WriteLine($"快捷方式已存在: {shortcutInfo.Name}");
                }
            }
            else
            {
                StatusText = "添加失败：无法创建快捷方式";
                Debug.WriteLine("ShortcutService 返回 null，无法创建快捷方式");
            }
        }
        catch (Exception ex)
        {
            StatusText = $"添加失败: {ex.Message}";
            Debug.WriteLine($"处理拖拽文件异常: {ex.Message}");
            Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
        }
        finally
        {
            IsLoading = false;
            Debug.WriteLine("拖拽文件处理完成");
        }
    }



    /// <summary>
    /// 删除快捷方式
    /// </summary>
    /// <param name="shortcut">快捷方式</param>
    [RelayCommand]
    public async Task DeleteShortcutAsync(ShortcutDisplayItem shortcut)
    {
        if (shortcut?.ShortcutInfo == null) return;

        try
        {
            StatusText = $"正在删除 {shortcut.DisplayName}...";
            var success = await _shortcutService.DeleteShortcutAsync(shortcut.ShortcutInfo);

            if (success)
            {
                Shortcuts.Remove(shortcut);
                FilterShortcuts();
                StatusText = $"{shortcut.DisplayName} 删除成功";
            }
            else
            {
                StatusText = $"{shortcut.DisplayName} 删除失败";
            }
        }
        catch (Exception ex)
        {
            StatusText = $"删除失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 显示添加分类对话框
    /// </summary>
    [RelayCommand]
    public void ShowAddCategoryDialog()
    {
        NewCategoryName = "";
        IsAddCategoryDialogVisible = true;
    }

    /// <summary>
    /// 添加新分类
    /// </summary>
    [RelayCommand]
    public void AddCategory()
    {
        if (string.IsNullOrWhiteSpace(NewCategoryName))
        {
            StatusText = "分类名称不能为空";
            return;
        }

        if (Categories.Any(c => c.Name == NewCategoryName))
        {
            StatusText = "分类已存在";
            return;
        }

        Categories.Add(new CategoryItem(NewCategoryName));
        StatusText = $"已添加分类: {NewCategoryName}";
        IsAddCategoryDialogVisible = false;
        NewCategoryName = "";
    }

    /// <summary>
    /// 取消添加分类
    /// </summary>
    [RelayCommand]
    public void CancelAddCategory()
    {
        IsAddCategoryDialogVisible = false;
        NewCategoryName = "";
    }

    /// <summary>
    /// 将中文分类名映射到英文分类名
    /// </summary>
    private string MapChineseCategoryToEnglish(string chineseCategory)
    {
        return chineseCategory switch
        {
            "办公软件" => "PDF",
            "开发工具" => "Development",
            "系统工具" => "System",
            "游戏娱乐" => "Game",
            "图像处理" => "Creative",
            "网络工具" => "PDF", // 暂时映射到 PDF，因为没有对应的英文分类
            _ => "System" // 默认归类到系统工具
        };
    }


}

/// <summary>
/// 分类项
/// </summary>
public partial class CategoryItem : ObservableObject
{
    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private bool isSelected = false;

    public CategoryItem(string name)
    {
        Name = name;
    }
}

/// <summary>
/// 快捷方式显示项
/// </summary>
public class ShortcutDisplayItem : INotifyPropertyChanged
{
    public ShortcutInfo ShortcutInfo { get; }
    private Bitmap? _iconBitmap;

    public string Name => ShortcutInfo.Name;
    public string DisplayName => GetDisplayName();
    public string IconText => GetIconText();
    public string Category => ShortcutInfo.Category;
    public string? IconPath => ShortcutInfo.IconPath;
    public bool HasRealIcon => IconBitmap != null;

    public Bitmap? IconBitmap
    {
        get => _iconBitmap;
        private set
        {
            _iconBitmap = value;
            OnPropertyChanged(nameof(IconBitmap));
            OnPropertyChanged(nameof(HasRealIcon));
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    public ShortcutDisplayItem(ShortcutInfo shortcutInfo)
    {
        ShortcutInfo = shortcutInfo;
        // 异步加载图标，避免阻塞 UI
        _ = LoadIconAsync();
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 异步加载图标
    /// </summary>
    private async Task LoadIconAsync()
    {
        try
        {
            Debug.WriteLine($"开始加载图标: {Name}");
            Debug.WriteLine($"图标路径: {IconPath}");
            Debug.WriteLine($"文件存在: {(!string.IsNullOrEmpty(IconPath) && File.Exists(IconPath))}");

            if (!string.IsNullOrEmpty(IconPath) && File.Exists(IconPath))
            {
                // 在后台线程加载图标
                var bitmap = await Task.Run(() =>
                {
                    try
                    {
                        Debug.WriteLine($"正在加载图标文件: {IconPath}");

                        // 尝试使用文件流加载，这样更可靠
                        using (var fileStream = File.OpenRead(IconPath))
                        {
                            var bmp = new Bitmap(fileStream);
                            Debug.WriteLine($"图标加载成功: {IconPath}, 尺寸: {bmp.PixelSize}");
                            return bmp;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"加载图标失败: {IconPath}, 错误: {ex.Message}");
                        return null;
                    }
                });

                if (bitmap != null)
                {
                    // 确保在 UI 线程更新属性
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        IconBitmap = bitmap;
                        Debug.WriteLine($"图标设置完成: {Name}, HasRealIcon: {HasRealIcon}");
                    });
                }
                else
                {
                    Debug.WriteLine($"图标加载失败，将显示 Emoji 图标: {Name}");
                }
            }
            else
            {
                Debug.WriteLine($"跳过图标加载: {Name} - 路径为空或文件不存在");

                // 测试时显示详细信息
                Debug.WriteLine($"图标路径为空或文件不存在，将显示 Emoji 图标: {Name}");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"异步加载图标失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取显示名称（截断长名称）
    /// </summary>
    private string GetDisplayName()
    {
        if (Name.Length <= 8)
            return Name;
            
        return Name.Substring(0, 6) + "...";
    }
    
    /// <summary>
    /// 获取图标文本
    /// </summary>
    private string GetIconText()
    {
        var name = Name.ToLower();

        // 根据应用名称返回合适的图标，使用更简洁的图标
        if (name.Contains("chrome") || name.Contains("browser"))
            return "🌐";
        else if (name.Contains("word") || name.Contains("office"))
            return "📄";
        else if (name.Contains("excel"))
            return "📊";
        else if (name.Contains("powerpoint"))
            return "📽";
        else if (name.Contains("pdf") || name.Contains("acrobat"))
            return "📕";
        else if (name.Contains("photoshop") || name.Contains("ps"))
            return "🎨";
        else if (name.Contains("visual") || name.Contains("code") || name.Contains("vs"))
            return "💻";
        else if (name.Contains("game") || name.Contains("steam"))
            return "🎮";
        else if (name.Contains("music") || name.Contains("spotify"))
            return "🎵";
        else if (name.Contains("video") || name.Contains("player") || name.Contains("vlc"))
            return "🎬";
        else if (name.Contains("calculator") || name.Contains("calc"))
            return "🧮";
        else if (name.Contains("notepad") || name.Contains("text") || name.Contains("editor"))
            return "📝";
        else if (name.Contains("paint"))
            return "🎨";
        else if (name.Contains("mail") || name.Contains("outlook"))
            return "📧";
        else if (name.Contains("folder") || name.Contains("explorer") || name.Contains("file"))
            return "📁";
        else if (name.Contains("settings") || name.Contains("control") || name.Contains("config"))
            return "⚙️";
        else if (name.Contains("向日葵") || name.Contains("sunlogin"))
            return "☀️";
        else if (name.Contains("qq") || name.Contains("wechat") || name.Contains("微信"))
            return "💬";
        else if (name.Contains("firefox"))
            return "🦊";
        else if (name.Contains("edge"))
            return "🌐";
        else if (name.Contains("zip") || name.Contains("rar") || name.Contains("7z"))
            return "📦";
        else
            return "⚪"; // 使用简洁的圆形图标作为默认
    }
}
