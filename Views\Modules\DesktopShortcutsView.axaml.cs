using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using LSSOFT.ViewModels;

namespace LSSOFT.Views.Modules;

public partial class DesktopShortcutsView : UserControl
{
    private DesktopShortcutsViewModel? ViewModel => DataContext as DesktopShortcutsViewModel;

    public DesktopShortcutsView()
    {
        InitializeComponent();
        DataContext = new DesktopShortcutsViewModel();

        // 设置拖拽事件
        SetupDragAndDrop();

        // 设置双击事件
        SetupDoubleClickEvents();
    }

    /// <summary>
    /// 设置拖拽功能
    /// </summary>
    private void SetupDragAndDrop()
    {
        var dropArea = this.FindControl<Border>("DropArea");
        if (dropArea != null)
        {
            // 在 Avalonia 中设置拖拽支持
            DragDrop.SetAllowDrop(dropArea, true);
            dropArea.AddHandler(DragDrop.DragOverEvent, OnDragOver);
            dropArea.AddHandler(DragDrop.DropEvent, OnDrop);
            dropArea.AddHandler(DragDrop.DragEnterEvent, OnDragEnter);
            dropArea.AddHandler(DragDrop.DragLeaveEvent, OnDragLeave);
        }
    }

    /// <summary>
    /// 设置双击事件
    /// </summary>
    private void SetupDoubleClickEvents()
    {
        var appIconsContainer = this.FindControl<ItemsControl>("AppIconsContainer");
        if (appIconsContainer != null)
        {
            appIconsContainer.AddHandler(DoubleTappedEvent, OnAppIconDoubleClick);
        }
    }

    /// <summary>
    /// 拖拽进入事件
    /// </summary>
    private void OnDragEnter(object? sender, DragEventArgs e)
    {
        try
        {
            if (e.Data.Contains(DataFormats.Files))
            {
                e.DragEffects = DragDropEffects.Copy;

                // 添加视觉反馈
                if (sender is Border border)
                {
                    border.Classes.Add("dragover");
                }
            }
            else
            {
                e.DragEffects = DragDropEffects.None;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"拖拽进入事件处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 拖拽离开事件
    /// </summary>
    private void OnDragLeave(object? sender, DragEventArgs e)
    {
        try
        {
            // 移除视觉反馈
            if (sender is Border border)
            {
                border.Classes.Remove("dragover");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"拖拽离开事件处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 拖拽悬停事件
    /// </summary>
    private void OnDragOver(object? sender, DragEventArgs e)
    {
        try
        {
            if (e.Data.Contains(DataFormats.Files))
            {
                var files = e.Data.GetFiles();
                if (files != null && files.Any(f => IsExecutableFile(f.Path.LocalPath)))
                {
                    e.DragEffects = DragDropEffects.Copy;
                }
                else
                {
                    e.DragEffects = DragDropEffects.None;
                }
            }
            else
            {
                e.DragEffects = DragDropEffects.None;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"拖拽悬停事件处理失败: {ex.Message}");
            e.DragEffects = DragDropEffects.None;
        }
    }

    /// <summary>
    /// 拖拽放置事件
    /// </summary>
    private async void OnDrop(object? sender, DragEventArgs e)
    {
        try
        {
            Debug.WriteLine("拖拽放置事件触发");

            // 移除视觉反馈
            if (sender is Border border)
            {
                border.Classes.Remove("dragover");
            }

            if (e.Data.Contains(DataFormats.Files))
            {
                var files = e.Data.GetFiles();
                Debug.WriteLine($"拖拽文件数量: {files?.Count() ?? 0}");

                if (files != null)
                {
                    foreach (var file in files)
                    {
                        var filePath = file.Path.LocalPath;
                        Debug.WriteLine($"拖拽文件路径: {filePath}");
                        Debug.WriteLine($"文件扩展名: {Path.GetExtension(filePath)}");
                        Debug.WriteLine($"是否为可执行文件: {IsExecutableFile(filePath)}");

                        if (IsExecutableFile(filePath))
                        {
                            await HandleDroppedFile(filePath);
                        }
                        else
                        {
                            Debug.WriteLine($"跳过不支持的文件: {filePath}");
                        }
                    }
                }
            }
            else
            {
                Debug.WriteLine("拖拽数据不包含文件");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"拖拽放置事件处理失败: {ex.Message}");
            Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// 处理拖拽的文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    private async Task HandleDroppedFile(string filePath)
    {
        try
        {
            if (ViewModel != null)
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                Debug.WriteLine($"处理拖拽文件: {fileName} ({filePath})");

                // 调用 ViewModel 的方法来添加快捷方式
                await ViewModel.HandleDroppedFileAsync(filePath);
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"处理拖拽文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查是否为可执行文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否为可执行文件</returns>
    private bool IsExecutableFile(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return false;

        var extension = Path.GetExtension(filePath).ToLower();
        return extension == ".exe" || extension == ".lnk" || extension == ".bat" || extension == ".cmd";
    }

    /// <summary>
    /// 应用图标双击事件
    /// </summary>
    private async void OnAppIconDoubleClick(object? sender, TappedEventArgs e)
    {
        try
        {
            // 查找被点击的数据项
            if (e.Source is Control control)
            {
                var dataContext = control.DataContext;
                if (dataContext is ShortcutDisplayItem shortcutItem && ViewModel != null)
                {
                    Debug.WriteLine($"双击启动应用: {shortcutItem.DisplayName}");
                    await ViewModel.LaunchApplicationAsync(shortcutItem);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"双击事件处理失败: {ex.Message}");
        }
    }
}
