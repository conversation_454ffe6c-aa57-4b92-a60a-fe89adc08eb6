using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Threading.Tasks;
using Avalonia;
using LSSOFT.ViewModels;
using LSSOFT.Models;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using System;
using System.IO;
using System.Collections.Generic;
using System.Timers;
using Avalonia.Platform.Storage;

namespace LSSOFT.Views.Modules;

public partial class SoftwareLocalizationView : UserControl
{
    private SoftwareLocalizationViewModel? _viewModel;

    public SoftwareLocalizationView()
    {
        try
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化开始");

            var databaseService = new LSSOFT.Services.DatabaseService();
            _viewModel = new SoftwareLocalizationViewModel(databaseService);
            this.DataContext = _viewModel;

            // 绑定事件
            if (this.FindControl<Button>("SelectPathButton") is Button btn)
                btn.Click += SelectPathButton_Click;

            // 绑定路径输入框事件，支持手动输入磁盘根目录
            if (this.FindControl<TextBox>("PathTextBox") is TextBox pathTextBox)
            {
                pathTextBox.KeyDown += PathTextBox_KeyDown;
            }

            // 绑定调试清除按钮事件
            if (this.FindControl<Button>("ClearDebugButton") is Button clearDebugButton)
            {
                clearDebugButton.Click += ClearDebugButton_Click;
            }

            // 添加测试按钮来验证路径切换
            AddTestButtons();

            // 启动调试信息更新
            StartDebugMessageUpdater();

            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SoftwareLocalizationView 初始化失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 清除调试信息按钮点击事件
    /// </summary>
    private void ClearDebugButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            if (_viewModel != null)
            {
                _viewModel.DebugMessage = "调试信息已清除";
                System.Diagnostics.Debug.WriteLine("调试信息已清除");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清除调试信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 启动调试信息更新器
    /// </summary>
    private void StartDebugMessageUpdater()
    {
        try
        {
            // 创建一个定时器来更新调试信息
            var timer = new System.Timers.Timer(1000); // 每秒更新一次
            timer.Elapsed += (s, e) =>
            {
                try
                {
                    if (_viewModel != null)
                    {
                        var currentTime = DateTime.Now.ToString("HH:mm:ss");
                        var message = $"[{currentTime}] 当前路径: {_viewModel.ScanPath} | 状态: {_viewModel.ScanStatus}";

                        // 在UI线程更新
                        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                        {
                            // 仅在DebugMessage为空时刷新
                            if (string.IsNullOrWhiteSpace(_viewModel.DebugMessage))
                                _viewModel.DebugMessage = message;
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"更新调试信息失败: {ex.Message}");
                }
            };
            timer.Start();

            System.Diagnostics.Debug.WriteLine("调试信息更新器已启动");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"启动调试信息更新器失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加测试按钮来验证路径切换功能
    /// </summary>
    private void AddTestButtons()
    {
        try
        {
            // 创建测试按钮
            var testButton1 = new Button
            {
                Content = "测试C:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton1.Click += (s, e) => TestPathChange("C:\\");

            var testButton2 = new Button
            {
                Content = "测试D:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton2.Click += (s, e) => TestPathChange("D:\\");

            var testButton3 = new Button
            {
                Content = "测试E:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton3.Click += (s, e) => TestPathChange("E:\\");

            var testSelectButton = new Button
            {
                Content = "测试选择",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testSelectButton.Click += (s, e) => TestSelectPath();

            // 将测试按钮添加到界面（临时用于调试）
            System.Diagnostics.Debug.WriteLine("测试按钮已创建，可以通过代码调用TestPathChange方法");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加测试按钮失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试路径切换
    /// </summary>
    private void TestPathChange(string path)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换到: {path}");
            if (_viewModel != null && (Directory.Exists(path) || IsDriveRoot(path)))
            {
                System.Diagnostics.Debug.WriteLine($"当前路径: {_viewModel.ScanPath}");
                AppendDebugMessage($"[调试] 尝试切换到: {path}");
                _ = Task.Run(async () =>
                {
                    await _viewModel.ChangeScanPathAsync(path);
                });
                System.Diagnostics.Debug.WriteLine($"测试路径切换处理完成: {path}");
            }
            else
            {
                if (_viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("ViewModel为空，无法测试");
                    AppendDebugMessage("[调试] ViewModel为空，无法切换路径");
                }
                if (!Directory.Exists(path) && !IsDriveRoot(path))
                {
                    System.Diagnostics.Debug.WriteLine($"测试路径不存在: {path}");
                    AppendDebugMessage($"[调试] 路径不存在: {path}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换失败: {ex.Message}");
            AppendDebugMessage($"[调试] 路径切换异常: {ex.Message}");
        }
    }

    private bool IsDriveRoot(string path)
    {
        if (string.IsNullOrWhiteSpace(path)) return false;
        path = path.Trim();
        if (path.Length == 2 && char.IsLetter(path[0]) && path[1] == ':') return true;
        if (path.Length == 3 && char.IsLetter(path[0]) && path[1] == ':' && (path[2] == '\\' || path[2] == '/')) return true;
        return false;
    }

    /// <summary>
    /// 检查路径是否为实际的磁盘根目录（通过DirectoryInfo检查）
    /// </summary>
    private bool IsActualDriveRoot(string path)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(path)) return false;

            var dirInfo = new DirectoryInfo(path);
            // 如果父目录为null，说明这是根目录
            var isRoot = dirInfo.Parent == null;
            System.Diagnostics.Debug.WriteLine($"IsActualDriveRoot检查: '{path}' -> Parent={dirInfo.Parent?.FullName ?? "null"}, IsRoot={isRoot}");
            return isRoot;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"IsActualDriveRoot检查异常: {path} -> {ex.Message}");
            return false;
        }
    }

    private void AppendDebugMessage(string msg)
    {
        if (_viewModel == null) return;
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            if (string.IsNullOrWhiteSpace(_viewModel.DebugMessage))
                _viewModel.DebugMessage = msg;
            else
                _viewModel.DebugMessage += "\n" + msg;
        });
    }

    /// <summary>
    /// 测试选择路径功能
    /// </summary>
    private void TestSelectPath()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("开始测试选择路径功能...");

            // 直接调用选择路径按钮的逻辑
            SelectPathButton_Click(null, new RoutedEventArgs());

            System.Diagnostics.Debug.WriteLine("测试选择路径功能完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试选择路径功能失败: {ex.Message}");
        }
    }



    /// <summary>
    /// 路径输入框按键事件，支持快速输入磁盘根目录
    /// </summary>
    private void PathTextBox_KeyDown(object? sender, Avalonia.Input.KeyEventArgs e)
    {
        try
        {
            if (e.Key == Avalonia.Input.Key.Enter && sender is TextBox textBox)
            {
                var inputPath = textBox.Text?.Trim();
                if (!string.IsNullOrEmpty(inputPath))
                {
                    // 自动补全磁盘根目录格式
                    if (inputPath.Length == 1 && char.IsLetter(inputPath[0]))
                    {
                        inputPath = $"{inputPath.ToUpper()}:\\";
                        textBox.Text = inputPath;
                    }
                    else if (inputPath.Length == 2 && char.IsLetter(inputPath[0]) && inputPath[1] == ':')
                    {
                        inputPath = $"{inputPath}\\";
                        textBox.Text = inputPath;
                    }
                    System.Diagnostics.Debug.WriteLine($"准备验证路径: {inputPath}");
                    System.Diagnostics.Debug.WriteLine($"路径是否存在: {Directory.Exists(inputPath)}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel是否为空: {_viewModel == null}");
                    if ((Directory.Exists(inputPath) || IsDriveRoot(inputPath)) && _viewModel != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"手动输入路径: {inputPath}");
                        textBox.Text = inputPath;
                        AppendDebugMessage($"[调试] 手动输入切换到: {inputPath}");
                        _ = Task.Run(async () =>
                        {
                            await _viewModel.ChangeScanPathAsync(inputPath);
                        });
                        System.Diagnostics.Debug.WriteLine($"手动输入路径处理完成: {inputPath}");
                    }
                    else
                    {
                        if (!Directory.Exists(inputPath) && !IsDriveRoot(inputPath))
                        {
                            System.Diagnostics.Debug.WriteLine($"路径不存在: {inputPath}");
                            AppendDebugMessage($"[调试] 路径不存在: {inputPath}");
                        }
                        if (_viewModel == null)
                        {
                            System.Diagnostics.Debug.WriteLine("ViewModel为空");
                            AppendDebugMessage("[调试] ViewModel为空，无法切换路径");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路径输入处理失败: {ex.Message}");
            AppendDebugMessage($"[调试] 路径输入异常: {ex.Message}");
        }
    }

    private async void SelectPathButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            var window = this.VisualRoot as Window;
            if (window?.StorageProvider != null)
            {
                // 尝试获取一个建议的起始位置
            IStorageFolder? suggestedStartLocation = null;
            try
            {
                if (_viewModel != null && !string.IsNullOrWhiteSpace(_viewModel.ScanPath) && Directory.Exists(_viewModel.ScanPath))
                {
                    suggestedStartLocation = await window.StorageProvider.TryGetFolderFromPathAsync(_viewModel.ScanPath);
                }
                else
                {
                    // 尝试获取第一个逻辑驱动器作为备用起始位置
                    var drives = DriveInfo.GetDrives();
                    if (drives.Length > 0)
                    {
                        suggestedStartLocation = await window.StorageProvider.TryGetFolderFromPathAsync(drives[0].Name);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取建议起始位置失败: {ex.Message}");
            }

            var folders = await window.StorageProvider.OpenFolderPickerAsync(new Avalonia.Platform.Storage.FolderPickerOpenOptions
            {
                Title = "选择整理路径 - 支持选择磁盘根目录",
                AllowMultiple = false,
                SuggestedStartLocation = suggestedStartLocation
            });

                System.Diagnostics.Debug.WriteLine($"文件夹选择对话框返回结果: folders={folders}, count={folders?.Count ?? 0}");

                if (folders != null && folders.Count > 0)
                {
                    var selectedFolder = folders[0];
                    var path = selectedFolder.Path.LocalPath;

                    System.Diagnostics.Debug.WriteLine($"=== 路径选择详细信息 ===");
                    System.Diagnostics.Debug.WriteLine($"用户选择的路径: {path}");
                    System.Diagnostics.Debug.WriteLine($"路径类型: {selectedFolder.Path.GetType()}");
                    System.Diagnostics.Debug.WriteLine($"路径Scheme: {selectedFolder.Path.Scheme}");
                    System.Diagnostics.Debug.WriteLine($"路径AbsolutePath: {selectedFolder.Path.AbsolutePath}");
                    System.Diagnostics.Debug.WriteLine($"路径OriginalString: {selectedFolder.Path.OriginalString}");
                    System.Diagnostics.Debug.WriteLine($"路径ToString: {selectedFolder.Path.ToString()}");
                    System.Diagnostics.Debug.WriteLine($"文件夹Name: {selectedFolder.Name}");
                    System.Diagnostics.Debug.WriteLine($"========================");

                    // 验证路径是否有效
                    System.Diagnostics.Debug.WriteLine($"准备验证选择的路径: {path}");

                    // 尝试不同的路径格式
                    var pathsToTry = new List<string> { path };

                    // 如果路径不是以反斜杠结尾，尝试添加反斜杠
                    if (!path.EndsWith("\\") && !path.EndsWith("/"))
                    {
                        pathsToTry.Add(path + "\\");
                    }

                    // 如果路径包含正斜杠，尝试转换为反斜杠
                    if (path.Contains("/"))
                    {
                        pathsToTry.Add(path.Replace("/", "\\"));
                        pathsToTry.Add(path.Replace("/", "\\") + "\\");
                    }

                    // 特殊处理：检查是否为盘符根目录的不同表示形式
                    try
                    {
                        var fullPath = Path.GetFullPath(path);
                        pathsToTry.Add(fullPath);
                        System.Diagnostics.Debug.WriteLine($"GetFullPath结果: {fullPath}");

                        // 检查是否为根目录
                        var dirInfo = new DirectoryInfo(fullPath);
                        if (dirInfo.Parent == null)
                        {
                            // 这是一个根目录，添加标准格式
                            var driveLetter = char.ToUpper(fullPath[0]);
                            pathsToTry.Add($"{driveLetter}:\\");
                            System.Diagnostics.Debug.WriteLine($"检测到根目录，添加标准格式: {driveLetter}:\\");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"路径标准化失败: {ex.Message}");
                    }

                    System.Diagnostics.Debug.WriteLine($"所有待测试路径: {string.Join(", ", pathsToTry)}");

                    string? validPath = null;
                    foreach (var testPath in pathsToTry)
                    {
                        System.Diagnostics.Debug.WriteLine($"测试路径: '{testPath}'");
                        System.Diagnostics.Debug.WriteLine($"  Directory.Exists: {Directory.Exists(testPath)}");
                        System.Diagnostics.Debug.WriteLine($"  IsDriveRoot: {IsDriveRoot(testPath)}");

                        if (Directory.Exists(testPath) || IsDriveRoot(testPath))
                        {
                            // 标准化盘符根目录为 F:\ 格式
                            if (IsDriveRoot(testPath) || IsActualDriveRoot(testPath))
                            {
                                var driveLetter = char.ToUpper(testPath[0]);
                                validPath = $"{driveLetter}:\\";
                                System.Diagnostics.Debug.WriteLine($"识别为盘符根目录，标准化为: {validPath}");
                                break;
                            }
                            else
                            {
                                validPath = testPath;
                                System.Diagnostics.Debug.WriteLine($"识别为普通目录: {validPath}");
                                break;
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"最终有效路径: {validPath}");
                    AppendDebugMessage($"[调试] 最终有效路径: {validPath}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel是否为空: {_viewModel == null}");
                    AppendDebugMessage($"[调试] ViewModel是否为空: {_viewModel == null}");

                    if (!string.IsNullOrEmpty(validPath))
                    {
                        if (_viewModel != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"选择路径: {validPath}");

                            try
                            {
                                // 更新调试信息
                                _viewModel.DebugMessage = $"选择路径: {validPath} | 时间: {DateTime.Now:HH:mm:ss}";

                                System.Diagnostics.Debug.WriteLine($"当前ScanPath: '{_viewModel.ScanPath}' -> 新路径: '{validPath}'");

                                // 强制更新路径 - 直接设置，我们已经在UI线程中
                                var oldPath = _viewModel.ScanPath;

                                System.Diagnostics.Debug.WriteLine($"=== 开始强制路径切换 ===");
                                System.Diagnostics.Debug.WriteLine($"旧路径: '{oldPath}'");
                                System.Diagnostics.Debug.WriteLine($"新路径: '{validPath}'");

                                // 先强制更新TextBox显示
                                if (this.FindControl<TextBox>("PathTextBox") is TextBox pathTextBox)
                                {
                                    pathTextBox.Text = validPath;
                                    System.Diagnostics.Debug.WriteLine($"TextBox文本已更新为: {pathTextBox.Text}");
                                }

                                // 强制清空旧路径，确保属性变化被检测到
                                if (oldPath == validPath)
                                {
                                    System.Diagnostics.Debug.WriteLine("路径相同，先清空再设置以强制触发变化");
                                    _viewModel.ScanPath = "";
                                    System.Diagnostics.Debug.WriteLine($"路径已清空: '{_viewModel.ScanPath}'");
                                }

                                // 然后设置ViewModel属性，这会触发扫描
                                _viewModel.ScanPath = validPath;
                                System.Diagnostics.Debug.WriteLine($"ViewModel.ScanPath已设置为: '{_viewModel.ScanPath}'");

                                System.Diagnostics.Debug.WriteLine($"路径设置完成: '{oldPath}' -> '{_viewModel.ScanPath}'");

                                // 验证路径是否真的改变了
                                if (_viewModel.ScanPath == validPath)
                                {
                                    _viewModel.DebugMessage = $"✅ 路径切换成功: {validPath} | {DateTime.Now:HH:mm:ss}";
                                    System.Diagnostics.Debug.WriteLine($"✅ 路径切换验证成功: {validPath}");

                                    // 再次验证TextBox是否显示正确
                                    if (this.FindControl<TextBox>("PathTextBox") is TextBox tb && tb.Text != validPath)
                                    {
                                        tb.Text = validPath;
                                        System.Diagnostics.Debug.WriteLine($"强制同步TextBox显示: {validPath}");
                                    }
                                }
                                else
                                {
                                    _viewModel.DebugMessage = $"❌ 路径切换失败: 期望{validPath}, 实际{_viewModel.ScanPath} | {DateTime.Now:HH:mm:ss}";
                                    System.Diagnostics.Debug.WriteLine($"❌ 路径切换验证失败: 期望{validPath}, 实际{_viewModel.ScanPath}");
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"路径设置异常: {ex.Message}");
                                _viewModel.DebugMessage = $"❌ 路径切换异常: {ex.Message} | {DateTime.Now:HH:mm:ss}";
                            }

                            System.Diagnostics.Debug.WriteLine($"选择路径处理完成: {validPath}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("ViewModel为空，无法更新路径");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"选择的路径无效: {path}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("用户取消了路径选择或没有选择任何文件夹");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("StorageProvider不可用");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"选择路径失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
        }
    }

    private async void OnShowDetailClick(object? sender, RoutedEventArgs e)
    {
        if (sender is Button btn && btn.CommandParameter is SoftwarePackageInfo info)
        {
            var uri = new Uri("avares://LSSOFT/Assets/avalonia-logo.ico");
            Bitmap? iconBitmap = null;
            try
            {
                using var stream = AssetLoader.Open(uri);
                iconBitmap = new Bitmap(stream);
            }
            catch { }
            var stack = new StackPanel { Margin = new Thickness(20), Spacing = 12 };
            if (iconBitmap != null)
                stack.Children.Add(new Image { Source = iconBitmap, Width = 48, Height = 48 });
            stack.Children.Add(new TextBlock { Text = $"软件名称：{info.Name}", FontWeight = Avalonia.Media.FontWeight.Bold, FontSize = 16 });
            stack.Children.Add(new TextBlock { Text = $"文件路径：{info.FilePath}", FontSize = 12, Foreground = Avalonia.Media.Brushes.Gray });
            stack.Children.Add(new TextBlock { Text = $"文件大小：{info.Size}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"版本信息：{info.Version ?? "-"}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"软件描述：{info.Description}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"创建时间：{info.CreatedDate:yyyy-MM-dd HH:mm:ss}", FontSize = 13 });
            var dialog = new Window
            {
                Title = "软件详细信息",
                Width = 400,
                Height = 320,
                Content = stack
            };
            if (this.VisualRoot is Window parent)
                await dialog.ShowDialog(parent);
        }
    }
}
