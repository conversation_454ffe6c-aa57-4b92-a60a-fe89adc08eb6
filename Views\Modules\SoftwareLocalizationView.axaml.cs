using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Threading.Tasks;
using Avalonia;
using LSSOFT.ViewModels;
using LSSOFT.Models;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using System;
using System.IO;
using System.Collections.Generic;
using System.Timers;
using Avalonia.Platform.Storage;
using Avalonia.Input;
using Avalonia.VisualTree;
using Avalonia.LogicalTree;
using System.Linq;
using Avalonia.Media;
using Avalonia.Data.Converters;
using System.Globalization;
using Avalonia.Layout;

namespace LSSOFT.Views.Modules;

// 简单的转换器，将分类名称和软件对象组合
public class CategorySoftwareConverter : IMultiValueConverter
{
    public static readonly CategorySoftwareConverter Instance = new();

    public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
    {
        if (values.Count == 2 && values[0] is string categoryName && values[1] is SoftwarePackageInfo software)
        {
            return new { CategoryName = categoryName, Software = software };
        }
        return null;
    }

    public object[] ConvertBack(object? value, Type[] targetTypes, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public partial class SoftwareLocalizationView : UserControl
{
    private SoftwareLocalizationViewModel? _viewModel;
    private SoftwarePackageInfo? _currentContextSoftware; // 存储当前右键的软件
    private string? _currentContextCategory; // 存储当前右键的分类
    private bool _isDragging = false;
    private Point _dragStartPoint;
    private string? _draggedCategoryName;

    public SoftwareLocalizationView()
    {
        try
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化开始");

            var databaseService = new LSSOFT.Services.DatabaseService();
            var shortcutService = new LSSOFT.Services.ShortcutService();
            _viewModel = new SoftwareLocalizationViewModel(databaseService, shortcutService);
            this.DataContext = _viewModel;

            // 绑定事件
            if (this.FindControl<Button>("SelectPathButton") is Button btn)
                btn.Click += SelectPathButton_Click;

            // 绑定路径输入框事件，支持手动输入磁盘根目录
            if (this.FindControl<TextBox>("PathTextBox") is TextBox pathTextBox)
            {
                pathTextBox.KeyDown += PathTextBox_KeyDown;
            }

            // 绑定调试清除按钮事件
            if (this.FindControl<Button>("ClearDebugButton") is Button clearDebugButton)
            {
                clearDebugButton.Click += ClearDebugButton_Click;
            }

            // 添加测试按钮来验证路径切换
            AddTestButtons();

            // 启动调试信息更新
            StartDebugMessageUpdater();

            // 添加拖拽事件处理器
            this.AddHandler(DragDrop.DragOverEvent, OnSoftwareItemDragOver);
            this.AddHandler(DragDrop.DropEvent, OnSoftwareItemDrop);

            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SoftwareLocalizationView 初始化失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 清除调试信息按钮点击事件
    /// </summary>
    private void ClearDebugButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            if (_viewModel != null)
            {
                _viewModel.DebugMessage = "调试信息已清除";
                System.Diagnostics.Debug.WriteLine("调试信息已清除");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清除调试信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 启动调试信息更新器
    /// </summary>
    private void StartDebugMessageUpdater()
    {
        try
        {
            // 创建一个定时器来更新调试信息
            var timer = new System.Timers.Timer(1000); // 每秒更新一次
            timer.Elapsed += (s, e) =>
            {
                try
                {
                    if (_viewModel != null)
                    {
                        var currentTime = DateTime.Now.ToString("HH:mm:ss");
                        var message = $"[{currentTime}] 当前路径: {_viewModel.ScanPath} | 状态: {_viewModel.ScanStatus}";

                        // 在UI线程更新
                        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                        {
                            // 仅在DebugMessage为空时刷新
                            if (string.IsNullOrWhiteSpace(_viewModel.DebugMessage))
                                _viewModel.DebugMessage = message;
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"更新调试信息失败: {ex.Message}");
                }
            };
            timer.Start();

            System.Diagnostics.Debug.WriteLine("调试信息更新器已启动");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"启动调试信息更新器失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加测试按钮来验证路径切换功能
    /// </summary>
    private void AddTestButtons()
    {
        try
        {
            // 创建测试按钮
            var testButton1 = new Button
            {
                Content = "测试C:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton1.Click += (s, e) => TestPathChange("C:\\");

            var testButton2 = new Button
            {
                Content = "测试D:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton2.Click += (s, e) => TestPathChange("D:\\");

            var testButton3 = new Button
            {
                Content = "测试E:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton3.Click += (s, e) => TestPathChange("E:\\");

            var testSelectButton = new Button
            {
                Content = "测试选择",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testSelectButton.Click += (s, e) => TestSelectPath();

            // 将测试按钮添加到界面（临时用于调试）
            System.Diagnostics.Debug.WriteLine("测试按钮已创建，可以通过代码调用TestPathChange方法");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加测试按钮失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试路径切换
    /// </summary>
    private void TestPathChange(string path)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换到: {path}");
            if (_viewModel != null && (Directory.Exists(path) || IsDriveRoot(path)))
            {
                System.Diagnostics.Debug.WriteLine($"当前路径: {_viewModel.ScanPath}");
                AppendDebugMessage($"[调试] 尝试切换到: {path}");
                _ = Task.Run(async () =>
                {
                    await _viewModel.ChangeScanPathAsync(path);
                });
                System.Diagnostics.Debug.WriteLine($"测试路径切换处理完成: {path}");
            }
            else
            {
                if (_viewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine("ViewModel为空，无法测试");
                    AppendDebugMessage("[调试] ViewModel为空，无法切换路径");
                }
                if (!Directory.Exists(path) && !IsDriveRoot(path))
                {
                    System.Diagnostics.Debug.WriteLine($"测试路径不存在: {path}");
                    AppendDebugMessage($"[调试] 路径不存在: {path}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换失败: {ex.Message}");
            AppendDebugMessage($"[调试] 路径切换异常: {ex.Message}");
        }
    }

    private bool IsDriveRoot(string path)
    {
        if (string.IsNullOrWhiteSpace(path)) return false;
        path = path.Trim();
        if (path.Length == 2 && char.IsLetter(path[0]) && path[1] == ':') return true;
        if (path.Length == 3 && char.IsLetter(path[0]) && path[1] == ':' && (path[2] == '\\' || path[2] == '/')) return true;
        return false;
    }

    /// <summary>
    /// 检查路径是否为实际的磁盘根目录（通过DirectoryInfo检查）
    /// </summary>
    private bool IsActualDriveRoot(string path)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(path)) return false;

            var dirInfo = new DirectoryInfo(path);
            // 如果父目录为null，说明这是根目录
            var isRoot = dirInfo.Parent == null;
            System.Diagnostics.Debug.WriteLine($"IsActualDriveRoot检查: '{path}' -> Parent={dirInfo.Parent?.FullName ?? "null"}, IsRoot={isRoot}");
            return isRoot;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"IsActualDriveRoot检查异常: {path} -> {ex.Message}");
            return false;
        }
    }

    private void AppendDebugMessage(string msg)
    {
        if (_viewModel == null) return;
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            if (string.IsNullOrWhiteSpace(_viewModel.DebugMessage))
                _viewModel.DebugMessage = msg;
            else
                _viewModel.DebugMessage += "\n" + msg;
        });
    }

    /// <summary>
    /// 测试选择路径功能
    /// </summary>
    private void TestSelectPath()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("开始测试选择路径功能...");

            // 直接调用选择路径按钮的逻辑
            SelectPathButton_Click(null, new RoutedEventArgs());

            System.Diagnostics.Debug.WriteLine("测试选择路径功能完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试选择路径功能失败: {ex.Message}");
        }
    }



    /// <summary>
    /// 路径输入框按键事件，支持快速输入磁盘根目录
    /// </summary>
    private void PathTextBox_KeyDown(object? sender, Avalonia.Input.KeyEventArgs e)
    {
        try
        {
            if (e.Key == Avalonia.Input.Key.Enter && sender is TextBox textBox)
            {
                var inputPath = textBox.Text?.Trim();
                if (!string.IsNullOrEmpty(inputPath))
                {
                    // 自动补全磁盘根目录格式
                    if (inputPath.Length == 1 && char.IsLetter(inputPath[0]))
                    {
                        inputPath = $"{inputPath.ToUpper()}:\\";
                        textBox.Text = inputPath;
                    }
                    else if (inputPath.Length == 2 && char.IsLetter(inputPath[0]) && inputPath[1] == ':')
                    {
                        inputPath = $"{inputPath}\\";
                        textBox.Text = inputPath;
                    }
                    System.Diagnostics.Debug.WriteLine($"准备验证路径: {inputPath}");
                    System.Diagnostics.Debug.WriteLine($"路径是否存在: {Directory.Exists(inputPath)}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel是否为空: {_viewModel == null}");
                    if ((Directory.Exists(inputPath) || IsDriveRoot(inputPath)) && _viewModel != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"手动输入路径: {inputPath}");
                        textBox.Text = inputPath;
                        AppendDebugMessage($"[调试] 手动输入切换到: {inputPath}");
                        _ = Task.Run(async () =>
                        {
                            await _viewModel.ChangeScanPathAsync(inputPath);
                        });
                        System.Diagnostics.Debug.WriteLine($"手动输入路径处理完成: {inputPath}");
                    }
                    else
                    {
                        if (!Directory.Exists(inputPath) && !IsDriveRoot(inputPath))
                        {
                            System.Diagnostics.Debug.WriteLine($"路径不存在: {inputPath}");
                            AppendDebugMessage($"[调试] 路径不存在: {inputPath}");
                        }
                        if (_viewModel == null)
                        {
                            System.Diagnostics.Debug.WriteLine("ViewModel为空");
                            AppendDebugMessage("[调试] ViewModel为空，无法切换路径");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路径输入处理失败: {ex.Message}");
            AppendDebugMessage($"[调试] 路径输入异常: {ex.Message}");
        }
    }

    private async void SelectPathButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            var window = this.VisualRoot as Window;
            if (window?.StorageProvider.CanPickFolder is true)
            {
                var folders = await window.StorageProvider.OpenFolderPickerAsync(new FolderPickerOpenOptions
                {
                    Title = "选择整理路径",
                    AllowMultiple = false
                    // 移除 SuggestedStartLocation，让系统提供默认（通常是“此电脑”或“快速访问”）
                    // 这为用户提供了最佳的驱动器切换体验
                });

                if (folders is { Count: > 0 })
                {
                    var pathUri = folders[0].Path;
                    string path;

                    try
                    {
                        // 尝试获取本地路径
                        path = pathUri.IsAbsoluteUri ? pathUri.LocalPath : Uri.UnescapeDataString(pathUri.ToString());

                        // 处理磁盘根目录的特殊情况
                        if (path.Length > 0 && path.EndsWith(":"))
                        {
                            path += "\\";
                        }

                        // 验证路径是否有效且存在
                        if (_viewModel != null && !string.IsNullOrWhiteSpace(path) && (Directory.Exists(path) || IsDriveRoot(path)))
                        {
                            System.Diagnostics.Debug.WriteLine($"路径选择成功: {path}");
                            _viewModel.ScanPath = path;
                            PathTextBox.Text = path;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"选择的路径无效或不存在: {path}");
                            if (_viewModel != null)
                            {
                                _viewModel.DebugMessage = $"错误：选择的路径无效 '{path}'";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理路径时发生异常: {ex.Message}");
                        if (_viewModel != null)
                        {
                            _viewModel.DebugMessage = $"错误: {ex.Message}";
                        }
                    }

                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("用户取消了路径选择。");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("StorageProvider 不可用或不支持文件夹选择。");
                if (_viewModel != null)
                {
                    _viewModel.DebugMessage = "错误：无法打开文件夹选择器。";
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"选择路径时发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            if (_viewModel != null)
            {
                _viewModel.DebugMessage = $"错误: {ex.Message}";
            }
        }
    }

    private async void OnShowDetailClick(object? sender, RoutedEventArgs e)
    {
        if (sender is Button btn && btn.CommandParameter is SoftwarePackageInfo info)
        {
            var uri = new Uri("avares://LSSOFT/Assets/avalonia-logo.ico");
            Bitmap? iconBitmap = null;
            try
            {
                using var stream = AssetLoader.Open(uri);
                iconBitmap = new Bitmap(stream);
            }
            catch { }
            var stack = new StackPanel { Margin = new Thickness(20), Spacing = 12 };
            if (iconBitmap != null)
                stack.Children.Add(new Image { Source = iconBitmap, Width = 48, Height = 48 });
            stack.Children.Add(new TextBlock { Text = $"软件名称：{info.Name}", FontWeight = Avalonia.Media.FontWeight.Bold, FontSize = 16 });
            stack.Children.Add(new TextBlock { Text = $"文件路径：{info.FilePath}", FontSize = 12, Foreground = Avalonia.Media.Brushes.Gray });
            stack.Children.Add(new TextBlock { Text = $"文件大小：{info.Size}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"版本信息：{info.Version ?? "-"}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"软件描述：{info.Description}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"创建时间：{info.CreatedDate:yyyy-MM-dd HH:mm:ss}", FontSize = 13 });
            var dialog = new Window
            {
                Title = "软件详细信息",
                Width = 400,
                Height = 320,
                Content = stack
            };
            if (this.VisualRoot is Window parent)
                await dialog.ShowDialog(parent);
        }
    }

    // 分类按钮点击事件处理
    private void OnCategoryTapped(object? sender, TappedEventArgs e)
    {
        if (sender is Border border && border.DataContext is SoftwareCategoryInfo category)
        {
            _viewModel?.FilterByCategoryCommand.Execute(category.Name);
            System.Diagnostics.Debug.WriteLine($"分类按钮点击: {category.Name}");
        }
    }

    // 分类按钮拖拽事件处理
    private void OnCategoryPointerPressed(object? sender, PointerPressedEventArgs e)
    {
        if (sender is Border border && border.DataContext is SoftwareCategoryInfo category)
        {
            _dragStartPoint = e.GetPosition(border);
            _draggedCategoryName = category.Name;
            _isDragging = false;
            System.Diagnostics.Debug.WriteLine($"分类按钮按下: {category.Name}");
        }
    }

    private async void OnCategoryPointerMoved(object? sender, PointerEventArgs e)
    {
        if (sender is Border border && !string.IsNullOrEmpty(_draggedCategoryName) &&
            e.GetCurrentPoint(border).Properties.IsLeftButtonPressed)
        {
            var currentPoint = e.GetPosition(border);
            var distance = Math.Sqrt(Math.Pow(currentPoint.X - _dragStartPoint.X, 2) +
                                   Math.Pow(currentPoint.Y - _dragStartPoint.Y, 2));

            if (distance > 10 && !_isDragging) // 开始拖拽的阈值
            {
                _isDragging = true;
                System.Diagnostics.Debug.WriteLine($"开始拖拽分类: {_draggedCategoryName}");

                try
                {
                    // 设置拖拽数据
                    var dragData = new DataObject();
                    dragData.Set("CategoryName", _draggedCategoryName);

                    // 开始拖拽操作
                    var result = await DragDrop.DoDragDrop(e, dragData, DragDropEffects.Copy);

                    System.Diagnostics.Debug.WriteLine($"拖拽操作完成，结果: {result}");

                    // 拖拽完成后清理所有列表项的高亮状态
                    ClearAllItemHighlights();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"拖拽操作异常: {ex.Message}");
                }
                finally
                {
                    _isDragging = false;
                    _draggedCategoryName = null;

                    // 确保清理高亮状态
                    ClearAllItemHighlights();
                }
            }
        }
    }

    private void OnCategoryPointerReleased(object? sender, PointerReleasedEventArgs e)
    {
        _isDragging = false;
        _draggedCategoryName = null;

        // 清除所有拖拽高亮状态
        ClearAllDragHighlights();
    }

    // 记录当前高亮的Border，用于清除
    private Border? _currentHighlightedBorder = null;

    /// <summary>
    /// 清除所有拖拽高亮状态
    /// </summary>
    private void ClearAllDragHighlights()
    {
        try
        {
            if (_currentHighlightedBorder != null)
            {
                // 恢复默认样式
                _currentHighlightedBorder.Background = Avalonia.Media.Brushes.White;
                _currentHighlightedBorder.BorderBrush = Avalonia.Media.Brush.Parse("#E5E7EB");
                _currentHighlightedBorder.BorderThickness = new Thickness(0, 0, 0, 1);

                System.Diagnostics.Debug.WriteLine("已清除高亮Border");
                _currentHighlightedBorder = null;
            }

            if (_viewModel != null)
            {
                _viewModel.DebugMessage = "已清除拖拽高亮状态";
                System.Diagnostics.Debug.WriteLine("已清除所有拖拽高亮状态");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清除高亮状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 通过备用方法尝试删除分类
    /// </summary>
    private void TryRemoveCategoryByAlternativeMethodOld(string categoryName)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"尝试通过备用方法删除分类: {categoryName}");

            // 由于无法直接获取软件信息，我们需要用户确认要从哪个软件删除分类
            // 这里可以显示一个对话框让用户选择，或者实现其他逻辑

            if (_viewModel != null)
            {
                _viewModel.DebugMessage = $"无法自动确定要从哪个软件删除分类 '{categoryName}'，请重试";
                System.Diagnostics.Debug.WriteLine("备用方法：无法确定目标软件");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"备用方法删除分类失败: {ex.Message}");
        }
    }

    // 软件列表项拖拽目标事件处理
    private void OnSoftwareItemDragOver(object? sender, DragEventArgs e)
    {
        if (e.Data.Contains("CategoryName"))
        {
            e.DragEffects = DragDropEffects.Copy;

            // 查找拖拽目标的Border控件
            var hitTest = this.InputHitTest(e.GetPosition(this));
            var border = FindParentBorder(hitTest as Control);

            // 如果目标Border与当前高亮的不同，先清除旧的高亮
            if (border != _currentHighlightedBorder)
            {
                // 清除之前的高亮
                if (_currentHighlightedBorder != null)
                {
                    _currentHighlightedBorder.Background = Avalonia.Media.Brushes.White;
                    _currentHighlightedBorder.BorderBrush = Avalonia.Media.Brush.Parse("#E5E7EB");
                    _currentHighlightedBorder.BorderThickness = new Thickness(0, 0, 0, 1);
                }

                // 设置新的高亮
                if (border != null)
                {
                    border.Background = Avalonia.Media.Brushes.LightBlue;
                    border.BorderBrush = Avalonia.Media.Brushes.Blue;
                    border.BorderThickness = new Thickness(2);
                    _currentHighlightedBorder = border;
                }
            }

            System.Diagnostics.Debug.WriteLine("拖拽悬停在软件列表项上");
        }
        else
        {
            e.DragEffects = DragDropEffects.None;
        }
    }

    private async void OnSoftwareItemDrop(object? sender, DragEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine("拖拽释放事件触发");

        if (e.Data.Contains("CategoryName"))
        {
            // 查找拖拽目标的Border控件
            var hitTest = this.InputHitTest(e.GetPosition(this));
            var border = FindParentBorder(hitTest as Control);

            // 恢复背景样式
            if (border != null)
            {
                border.Background = Avalonia.Media.Brushes.White;
                border.BorderBrush = Avalonia.Media.Brush.Parse("#E5E7EB");
                border.BorderThickness = new Thickness(0, 0, 0, 1);

                // 清除高亮记录
                if (_currentHighlightedBorder == border)
                {
                    _currentHighlightedBorder = null;
                }

                // 获取软件信息
                if (border.DataContext is SoftwarePackageInfo software)
                {
                    var categoryName = e.Data.Get("CategoryName") as string;
                    if (!string.IsNullOrEmpty(categoryName) && _viewModel != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"分配分类 '{categoryName}' 给软件 '{software.Name}'");

                        _viewModel.DraggedCategoryName = categoryName;
                        await _viewModel.AssignSoftwareCategoryCommand.ExecuteAsync(software);

                        _viewModel.DebugMessage = $"已将软件 '{software.Name}' 分配到分类 '{categoryName}'";
                    }
                }
            }
        }
    }

    private Border? FindParentBorder(Control? control)
    {
        while (control != null)
        {
            if (control is Border border && border.DataContext is SoftwarePackageInfo)
                return border;
            control = control.Parent as Control;
        }
        return null;
    }

    /// <summary>
    /// 清理所有列表项的高亮状态
    /// </summary>
    private void ClearAllItemHighlights()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("清理所有列表项的高亮状态");
            // 简单的方法：通过递归查找所有Border控件
            ClearHighlightsRecursive(this);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"清理高亮状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 递归清理高亮状态
    /// </summary>
    private void ClearHighlightsRecursive(Control control)
    {
        try
        {
            // 如果是Border且DataContext是SoftwarePackageInfo，恢复默认样式
            if (control is Border border && border.DataContext is SoftwarePackageInfo)
            {
                border.Background = Avalonia.Media.Brushes.White;
                border.BorderBrush = Avalonia.Media.Brush.Parse("#E5E7EB");
                border.BorderThickness = new Thickness(0, 0, 0, 1);
            }

            // 递归处理子控件
            if (control is Panel panel)
            {
                foreach (Control child in panel.Children)
                {
                    ClearHighlightsRecursive(child);
                }
            }
            else if (control is ContentControl contentControl && contentControl.Content is Control childControl)
            {
                ClearHighlightsRecursive(childControl);
            }
            else if (control is ItemsControl itemsControl)
            {
                // 对于ItemsControl，我们需要等待它渲染完成
                // 这里简化处理，只在需要时调用
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"递归清理失败: {ex.Message}");
        }
    }

    // 从软件中删除分类
    private async void OnRemoveCategoryFromSoftware(object? sender, RoutedEventArgs e)
    {
        if (sender is MenuItem menuItem && _viewModel != null)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始删除分类操作");

                // 获取分类名称
                if (menuItem.Tag is not string categoryName)
                {
                    System.Diagnostics.Debug.WriteLine("无法从Tag获取分类名称");
                    _viewModel.DebugMessage = "无法获取分类名称";
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"获取到分类名称: '{categoryName}'");

                // 通过改进的方法查找软件
                SoftwarePackageInfo? software = null;

                if (menuItem.Parent is ContextMenu contextMenu)
                {
                    System.Diagnostics.Debug.WriteLine($"ContextMenu PlacementTarget: {contextMenu.PlacementTarget?.GetType().Name}");

                    if (contextMenu.PlacementTarget != null)
                    {
                        software = FindSoftwareFromPlacementTargetImproved(contextMenu.PlacementTarget);
                        if (software != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ 找到软件: '{software.Name}'");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("❌ 改进方法未找到软件");
                        }
                    }
                }

                if (software == null)
                {
                    System.Diagnostics.Debug.WriteLine("尝试备用方法");
                    if (menuItem.Parent is ContextMenu contextMenuForBackup)
                    {
                        await TryRemoveCategoryByAlternativeMethod(categoryName, contextMenuForBackup);
                    }
                    else
                    {
                        _viewModel.DebugMessage = "无法获取上下文菜单信息";
                    }
                    return;
                }

                if (string.IsNullOrEmpty(categoryName) || software == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ 分类名称或软件信息为空");
                    _viewModel.DebugMessage = "分类名称或软件信息为空";
                    return;
                }

                // 直接使用从MultiBinding获取的软件信息删除分类
                System.Diagnostics.Debug.WriteLine($"准备从软件 '{software.Name}' 删除分类 '{categoryName}'");
                await RemoveCategoryFromSpecificSoftware(software, categoryName);


            }
            catch (Exception ex)
            {
                _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"删除分类异常: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 从PlacementTarget查找软件信息
    /// </summary>
    private SoftwarePackageInfo? FindSoftwareFromPlacementTarget(Control placementTarget)
    {
        System.Diagnostics.Debug.WriteLine($"开始从PlacementTarget查找软件: {placementTarget.GetType().Name}");

        // 向上遍历控件树查找包含SoftwarePackageInfo的Border
        Control? current = placementTarget;
        int level = 0;

        while (current != null && level < 15)
        {
            System.Diagnostics.Debug.WriteLine($"第{level}层: {current.GetType().Name}, DataContext类型: {current.DataContext?.GetType().Name}");

            if (current.DataContext is SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine($"✅ 在第{level}层找到软件: '{software.Name}'");
                return software;
            }

            // 尝试通过Parent查找
            current = current.Parent as Control ?? current.GetVisualParent() as Control;
            level++;
        }

        System.Diagnostics.Debug.WriteLine($"遍历了{level}层，未找到软件信息");
        return null;
    }

    /// <summary>
    /// 改进的PlacementTarget查找方法
    /// </summary>
    private SoftwarePackageInfo? FindSoftwareFromPlacementTargetImproved(Control placementTarget)
    {
        System.Diagnostics.Debug.WriteLine($"改进查找PlacementTarget: {placementTarget.GetType().Name}");

        // 方法1：直接检查PlacementTarget的DataContext
        if (placementTarget.DataContext is SoftwarePackageInfo directSoftware)
        {
            System.Diagnostics.Debug.WriteLine($"✅ 直接找到软件: {directSoftware.Name}");
            return directSoftware;
        }

        // 方法2：向上遍历可视化树，特别关注Border控件
        var current = placementTarget as Control;
        int level = 0;
        while (current != null && level < 15)
        {
            System.Diagnostics.Debug.WriteLine($"  Level {level}: {current.GetType().Name}, DataContext: {current.DataContext?.GetType().Name}");

            // 特别检查Border控件，因为软件项通常包装在Border中
            if (current is Border border && border.DataContext is SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine($"  ✅ 在Border Level {level}找到软件: {software.Name}");
                return software;
            }

            // 检查其他控件的DataContext
            if (current.DataContext is SoftwarePackageInfo otherSoftware)
            {
                System.Diagnostics.Debug.WriteLine($"  ✅ 在Level {level}找到软件: {otherSoftware.Name}");
                return otherSoftware;
            }

            // 向上遍历
            current = current.Parent as Control ?? current.GetVisualParent() as Control;
            level++;
        }

        System.Diagnostics.Debug.WriteLine("  ❌ 改进方法未找到软件");
        return null;
    }

    /// <summary>
    /// 从指定软件删除分类
    /// </summary>
    private async Task RemoveCategoryFromSpecificSoftware(SoftwarePackageInfo software, string categoryName)
    {
        System.Diagnostics.Debug.WriteLine($"准备从软件 '{software.Name}' 删除分类 '{categoryName}'");
        System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

        if (software.Categories.Contains(categoryName))
        {
            // 调用ViewModel的删除分类命令
            if (_viewModel != null)
            {
                await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                _viewModel.DebugMessage = $"已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
            }
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"软件 '{software.Name}' 中未找到分类 '{categoryName}'");
            if (_viewModel != null)
            {
                _viewModel.DebugMessage = $"软件 '{software.Name}' 中未找到分类 '{categoryName}'";
            }
        }
    }

    /// <summary>
    /// 尝试确定目标软件（当有多个候选时）
    /// </summary>
    private SoftwarePackageInfo? TryDetermineTargetSoftware(List<SoftwarePackageInfo> candidates, string categoryName, ContextMenu? contextMenu)
    {
        System.Diagnostics.Debug.WriteLine($"尝试从{candidates.Count}个候选软件中确定目标");

        // 方法1：尝试通过ContextMenu的PlacementTarget确定
        if (contextMenu?.PlacementTarget != null)
        {
            System.Diagnostics.Debug.WriteLine("尝试通过ContextMenu PlacementTarget确定目标");
            var targetSoftware = FindSoftwareFromPlacementTarget(contextMenu.PlacementTarget);
            if (targetSoftware != null && candidates.Contains(targetSoftware))
            {
                System.Diagnostics.Debug.WriteLine($"✅ 通过PlacementTarget确定目标软件: '{targetSoftware.Name}'");
                return targetSoftware;
            }
        }

        // 方法2：选择第一个候选（作为最后的备选方案）
        if (candidates.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine($"使用第一个候选作为目标: '{candidates[0].Name}'");
            return candidates[0];
        }

        return null;
    }



    /// <summary>
    /// 查找包含软件信息的父级Border
    /// </summary>
    private Border? FindParentSoftwareBorder(Control? control)
    {
        int level = 0;
        System.Diagnostics.Debug.WriteLine($"开始查找软件Border，起始控件: {control?.GetType().Name}");

        while (control != null && level < 10) // 限制遍历层数防止无限循环
        {
            System.Diagnostics.Debug.WriteLine($"第{level}层: {control.GetType().Name}, DataContext: {control.DataContext?.GetType().Name}");

            if (control is Border border && border.DataContext is SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine($"找到软件Border! 软件名称: {software.Name}");
                return border;
            }

            // 尝试通过Parent属性向上查找
            var parent = control.Parent as Control;
            if (parent == null)
            {
                // 如果Parent为null，尝试通过VisualParent查找
                parent = control.GetVisualParent() as Control;
                System.Diagnostics.Debug.WriteLine($"Parent为null，尝试VisualParent: {parent?.GetType().Name}");
            }

            control = parent;
            level++;
        }

        System.Diagnostics.Debug.WriteLine($"未找到软件Border，遍历了{level}层");
        return null;
    }

    /// <summary>
    /// 在可视化树中查找分类Border
    /// </summary>
    private Border? FindCategoryBorderInVisualTree(Control? control)
    {
        while (control != null)
        {
            if (control is Border border && border.DataContext is string)
            {
                return border;
            }
            control = control.GetVisualParent() as Control;
        }
        return null;
    }

    /// <summary>
    /// 通过备用方法尝试删除分类
    /// </summary>
    private async Task TryRemoveCategoryByAlternativeMethod(string categoryName, ContextMenu contextMenu)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"尝试备用方法删除分类: {categoryName}");

            // 方法1：通过LocalPackages集合查找包含该分类的软件
            var candidateSoftware = _viewModel?.LocalPackages?.Where(s => s.Categories.Contains(categoryName)).ToList() ?? new List<SoftwarePackageInfo>();

            System.Diagnostics.Debug.WriteLine($"找到 {candidateSoftware.Count} 个包含分类 '{categoryName}' 的软件");

            if (candidateSoftware.Count == 1)
            {
                var software = candidateSoftware[0];
                System.Diagnostics.Debug.WriteLine($"备用方法成功：通过分类匹配找到唯一软件 '{software.Name}'");
                System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

                // 调用ViewModel的删除分类命令
                if (_viewModel != null)
                {
                    await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                    _viewModel.DebugMessage = $"已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
                }
            }
            else if (candidateSoftware.Count > 1)
            {
                System.Diagnostics.Debug.WriteLine($"找到多个软件包含分类'{categoryName}':");
                foreach (var sw in candidateSoftware)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {sw.Name}");
                }

                // 尝试通过鼠标位置或其他方式确定目标软件
                var targetSoftware = TryDetermineTargetSoftware(candidateSoftware, categoryName, contextMenu);

                if (targetSoftware != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 智能选择目标软件: '{targetSoftware.Name}'");
                    if (_viewModel != null)
                    {
                        await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = targetSoftware, CategoryName = categoryName });
                        _viewModel.DebugMessage = $"已从软件 '{targetSoftware.Name}' 中删除分类 '{categoryName}' (智能选择)";
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 无法确定目标软件");
                    if (_viewModel != null)
                    {
                        _viewModel.DebugMessage = $"错误：找到多个包含分类'{categoryName}'的软件，无法确定删除目标。请直接在软件项上右键删除。";
                    }
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"备用方法失败：未找到包含分类'{categoryName}'的软件");
                if (_viewModel != null)
                {
                    _viewModel.DebugMessage = $"未找到包含分类'{categoryName}'的软件";
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"备用方法删除分类失败: {ex.Message}");
            if (_viewModel != null)
            {
                _viewModel.DebugMessage = $"备用方法删除分类失败: {ex.Message}";
            }
        }
    }

    // 新的删除分类方法 - 使用更可靠的上下文查找
    private async void OnRemoveCategoryFromSoftwareNew(object? sender, RoutedEventArgs e)
    {
        if (sender is not MenuItem menuItem || _viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("❌ MenuItem或ViewModel为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始新的删除分类操作 ===");

            // 获取分类名称
            if (menuItem.Tag is not string categoryName)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 无法从Tag获取分类名称，Tag类型: {menuItem.Tag?.GetType().Name ?? "null"}");
                _viewModel.DebugMessage = "无法获取分类名称";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ 获取到分类名称: '{categoryName}'");

            // 简化方案：直接弹出选择对话框让用户确认要删除哪个软件的分类
            var candidateSoftware = _viewModel.LocalPackages?.Where(s => s.Categories.Contains(categoryName)).ToList();

            if (candidateSoftware == null || candidateSoftware.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 没有找到包含分类 '{categoryName}' 的软件");
                _viewModel.DebugMessage = $"没有找到包含分类 '{categoryName}' 的软件";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ 找到 {candidateSoftware.Count} 个包含分类 '{categoryName}' 的软件:");
            foreach (var sw in candidateSoftware)
            {
                System.Diagnostics.Debug.WriteLine($"  - {sw.Name}");
            }

            SoftwarePackageInfo? targetSoftware = null;

            if (candidateSoftware.Count == 1)
            {
                // 只有一个软件包含此分类，直接使用
                targetSoftware = candidateSoftware[0];
                System.Diagnostics.Debug.WriteLine($"✅ 唯一匹配，选择软件: '{targetSoftware.Name}'");
            }
            else
            {
                // 多个软件包含此分类，尝试通过UI层次结构查找
                System.Diagnostics.Debug.WriteLine("🔍 多个软件包含此分类，尝试通过UI层次结构查找...");
                targetSoftware = FindSoftwareFromDataContextHierarchy(menuItem);

                if (targetSoftware == null)
                {
                    // 如果还是找不到，使用第一个作为默认（临时方案）
                    targetSoftware = candidateSoftware[0];
                    System.Diagnostics.Debug.WriteLine($"⚠️ UI层次结构查找失败，使用第一个软件作为默认: '{targetSoftware.Name}'");
                    _viewModel.DebugMessage = $"⚠️ 无法精确定位，已删除 '{targetSoftware.Name}' 的分类 '{categoryName}'";
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ UI层次结构查找成功: '{targetSoftware.Name}'");
                }
            }

            if (targetSoftware != null && targetSoftware.Categories.Contains(categoryName))
            {
                // 执行删除操作
                await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = targetSoftware, CategoryName = categoryName });
                _viewModel.DebugMessage = $"✅ 已从软件 '{targetSoftware.Name}' 中删除分类 '{categoryName}'";
                System.Diagnostics.Debug.WriteLine($"✅ 成功删除分类");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ 最终确定的软件中未找到分类 '{categoryName}'");
                _viewModel.DebugMessage = $"软件中未找到分类 '{categoryName}'";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 删除分类失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
            _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 通过DataContext层次结构查找软件信息
    /// </summary>
    private SoftwarePackageInfo? FindSoftwareFromDataContextHierarchy(MenuItem menuItem)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("开始通过DataContext层次结构查找软件");

            // 方法1：通过ContextMenu的PlacementTarget
            if (menuItem.Parent is ContextMenu contextMenu && contextMenu.PlacementTarget != null)
            {
                System.Diagnostics.Debug.WriteLine($"PlacementTarget类型: {contextMenu.PlacementTarget.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"PlacementTarget DataContext类型: {contextMenu.PlacementTarget.DataContext?.GetType().Name ?? "null"}");

                // 向上遍历可视化树查找软件DataContext
                var current = contextMenu.PlacementTarget;
                var level = 0;

                while (current != null && level < 20)
                {
                    System.Diagnostics.Debug.WriteLine($"Level {level}: {current.GetType().Name}, DataContext: {current.DataContext?.GetType().Name ?? "null"}");

                    // 检查当前元素的DataContext
                    if (current.DataContext is SoftwarePackageInfo software)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 在Level {level}找到软件DataContext: '{software.Name}'");
                        return software;
                    }

                    // 特别检查ItemsControl的情况
                    if (current is ItemsControl itemsControl && itemsControl.DataContext is SoftwarePackageInfo itemsSoftware)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 在ItemsControl Level {level}找到软件: '{itemsSoftware.Name}'");
                        return itemsSoftware;
                    }

                    // 检查是否是ListBoxItem或其他容器
                    if (current is ListBoxItem listBoxItem && listBoxItem.DataContext is SoftwarePackageInfo listBoxSoftware)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 在ListBoxItem Level {level}找到软件: '{listBoxSoftware.Name}'");
                        return listBoxSoftware;
                    }

                    // 检查是否是其他容器类型
                    if (current.GetType().Name.Contains("Presenter") && current.DataContext is SoftwarePackageInfo presenterSoftware)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 在{current.GetType().Name} Level {level}找到软件: '{presenterSoftware.Name}'");
                        return presenterSoftware;
                    }

                    // 向上查找父元素
                    if (current.Parent is Control parentControl)
                    {
                        current = parentControl;
                    }
                    else if (current.GetLogicalParent() is Control logicalParent)
                    {
                        current = logicalParent;
                    }
                    else
                    {
                        current = null;
                    }
                    level++;
                }

                System.Diagnostics.Debug.WriteLine($"❌ 遍历了{level}层，未找到软件DataContext");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ 无法获取ContextMenu或PlacementTarget");
            }

            // 方法2：备用方案 - 通过分类名称匹配（如果只有一个软件有这个分类）
            System.Diagnostics.Debug.WriteLine("尝试备用方案：通过分类名称匹配");
            if (menuItem.Tag is string categoryName && _viewModel?.LocalPackages != null)
            {
                var candidateSoftware = _viewModel.LocalPackages.Where(s => s.Categories.Contains(categoryName)).ToList();
                System.Diagnostics.Debug.WriteLine($"找到 {candidateSoftware.Count} 个包含分类 '{categoryName}' 的软件");

                if (candidateSoftware.Count == 1)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 备用方案成功：唯一匹配软件 '{candidateSoftware[0].Name}'");
                    return candidateSoftware[0];
                }
                else if (candidateSoftware.Count > 1)
                {
                    System.Diagnostics.Debug.WriteLine("❌ 备用方案失败：多个软件包含相同分类");
                    foreach (var sw in candidateSoftware)
                    {
                        System.Diagnostics.Debug.WriteLine($"  - {sw.Name}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine("❌ 所有方法都失败了");
            return null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"DataContext层次结构查找失败: {ex.Message}");
            return null;
        }
    }

    // 分类Border鼠标按下时记录当前软件信息
    private void OnCategoryBorderPointerPressed(object? sender, PointerPressedEventArgs e)
    {
        if (sender is not Border border)
        {
            System.Diagnostics.Debug.WriteLine("❌ Border为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 分类Border鼠标按下事件触发 ===");
            System.Diagnostics.Debug.WriteLine($"鼠标按钮: 左键={e.GetCurrentPoint(this).Properties.IsLeftButtonPressed}, 右键={e.GetCurrentPoint(this).Properties.IsRightButtonPressed}");

            // 记录分类信息
            if (border.DataContext is string categoryName)
            {
                _currentContextCategory = categoryName;
                System.Diagnostics.Debug.WriteLine($"✅ 记录当前分类: '{categoryName}'");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ Border DataContext不是字符串: {border.DataContext?.GetType().Name ?? "null"}");
            }

            // 手动向上查找ListBoxItem
            Visual? current = border;
            ListBoxItem? listBoxItem = null;
            int depth = 0;

            while (current != null && depth < 15)
            {
                System.Diagnostics.Debug.WriteLine($"查找层级 {depth}: {current.GetType().Name}");

                if (current is ListBoxItem item)
                {
                    listBoxItem = item;
                    System.Diagnostics.Debug.WriteLine($"✅ 找到ListBoxItem在层级 {depth}");
                    break;
                }

                current = current.GetVisualParent();
                depth++;
            }

            if (listBoxItem?.DataContext is SoftwarePackageInfo software)
            {
                _currentContextSoftware = software;
                System.Diagnostics.Debug.WriteLine($"✅ 记录当前软件: '{software.Name}'");
                System.Diagnostics.Debug.WriteLine($"软件文件路径: '{software.FilePath}'");
                System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");
            }
            else
            {
                _currentContextSoftware = null;
                System.Diagnostics.Debug.WriteLine($"❌ 无法找到软件，ListBoxItem DataContext类型: {listBoxItem?.DataContext?.GetType().Name ?? "null"}");
            }
        }
        catch (Exception ex)
        {
            _currentContextSoftware = null;
            _currentContextCategory = null;
            System.Diagnostics.Debug.WriteLine($"❌ 记录软件信息失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 异常堆栈: {ex.StackTrace}");
        }
    }

    // 最终的删除分类方法 - 使用预记录的软件和分类信息
    private async void OnRemoveCategoryFromSoftwareFinal(object? sender, RoutedEventArgs e)
    {
        if (sender is not MenuItem menuItem || _viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("❌ MenuItem或ViewModel为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始使用预记录信息的删除分类操作 ===");

            // 优先使用预记录的分类信息，否则从Tag获取
            string? categoryName = _currentContextCategory;
            if (string.IsNullOrEmpty(categoryName) && menuItem.Tag is string tagCategory)
            {
                categoryName = tagCategory;
                System.Diagnostics.Debug.WriteLine($"✅ 从Tag获取分类名称: '{categoryName}'");
            }
            else if (!string.IsNullOrEmpty(categoryName))
            {
                System.Diagnostics.Debug.WriteLine($"✅ 使用预记录的分类名称: '{categoryName}'");
            }

            if (string.IsNullOrEmpty(categoryName))
            {
                System.Diagnostics.Debug.WriteLine("❌ 无法获取分类名称");
                _viewModel.DebugMessage = "无法获取分类名称";
                return;
            }

            // 使用预记录的软件信息
            if (_currentContextSoftware == null)
            {
                System.Diagnostics.Debug.WriteLine("❌ 没有预记录的软件信息");
                _viewModel.DebugMessage = "无法确定目标软件";
                return;
            }

            var software = _currentContextSoftware;
            System.Diagnostics.Debug.WriteLine($"✅ 使用预记录的软件: '{software.Name}'");
            System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

            if (software.Categories.Contains(categoryName))
            {
                // 执行删除操作
                await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                _viewModel.DebugMessage = $"✅ 已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
                System.Diagnostics.Debug.WriteLine($"✅ 成功删除分类");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ 软件 '{software.Name}' 中未找到分类 '{categoryName}'");
                _viewModel.DebugMessage = $"软件 '{software.Name}' 中未找到分类 '{categoryName}'";
            }

            // 清除记录的信息
            _currentContextSoftware = null;
            _currentContextCategory = null;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 删除分类失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
            _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
            _currentContextSoftware = null;
            _currentContextCategory = null;
        }
    }

    // 终极的删除分类方法 - 使用Tag和CommandParameter分别传递参数
    private async void OnRemoveCategoryFromSoftwareUltimate(object? sender, RoutedEventArgs e)
    {
        if (sender is not MenuItem menuItem || _viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("❌ MenuItem或ViewModel为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始终极删除分类操作 ===");

            // 从Tag获取分类名称
            if (menuItem.Tag is not string categoryName)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 无法从Tag获取分类名称，Tag类型: {menuItem.Tag?.GetType().Name ?? "null"}");
                _viewModel.DebugMessage = "无法获取分类名称";
                return;
            }

            // 从CommandParameter获取软件对象
            if (menuItem.CommandParameter is not SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 无法从CommandParameter获取软件对象，类型: {menuItem.CommandParameter?.GetType().Name ?? "null"}");
                _viewModel.DebugMessage = "无法获取软件信息";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ 提取到软件: '{software.Name}'");
            System.Diagnostics.Debug.WriteLine($"✅ 提取到分类: '{categoryName}'");
            System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

            if (software.Categories.Contains(categoryName))
            {
                // 执行删除操作
                await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                _viewModel.DebugMessage = $"✅ 已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
                System.Diagnostics.Debug.WriteLine($"✅ 成功删除分类");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ 软件 '{software.Name}' 中未找到分类 '{categoryName}'");
                _viewModel.DebugMessage = $"软件 '{software.Name}' 中未找到分类 '{categoryName}'";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 删除分类失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
            _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
        }
    }

    // 直接的删除分类方法 - 使用转换器传递参数
    private async void OnRemoveCategoryFromSoftwareDirect(object? sender, RoutedEventArgs e)
    {
        if (sender is not MenuItem menuItem || _viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("❌ MenuItem或ViewModel为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始直接删除分类操作 ===");

            // 从Tag获取转换器生成的参数
            if (menuItem.Tag is not object tagObj)
            {
                System.Diagnostics.Debug.WriteLine("❌ MenuItem.Tag为null");
                _viewModel.DebugMessage = "无法获取删除参数";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ Tag类型: {tagObj.GetType().Name}");

            // 使用反射获取参数
            var categoryNameProp = tagObj.GetType().GetProperty("CategoryName");
            var softwareProp = tagObj.GetType().GetProperty("Software");

            if (categoryNameProp?.GetValue(tagObj) is not string categoryName ||
                softwareProp?.GetValue(tagObj) is not SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine("❌ 无法从Tag提取分类名称或软件对象");
                _viewModel.DebugMessage = "无法提取删除参数";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ 提取到软件: '{software.Name}'");
            System.Diagnostics.Debug.WriteLine($"✅ 提取到分类: '{categoryName}'");
            System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

            if (software.Categories.Contains(categoryName))
            {
                // 执行删除操作
                await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                _viewModel.DebugMessage = $"✅ 已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
                System.Diagnostics.Debug.WriteLine($"✅ 成功删除分类");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"❌ 软件 '{software.Name}' 中未找到分类 '{categoryName}'");
                _viewModel.DebugMessage = $"软件 '{software.Name}' 中未找到分类 '{categoryName}'";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 删除分类失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
            _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
        }
    }

    // 简单的删除分类方法 - 通过鼠标位置确定软件
    private async void OnRemoveCategoryFromSoftwareSimple(object? sender, RoutedEventArgs e)
    {
        if (sender is not MenuItem menuItem || _viewModel == null)
        {
            System.Diagnostics.Debug.WriteLine("❌ MenuItem或ViewModel为null");
            return;
        }

        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始简单删除分类操作 ===");

            // 获取分类名称
            if (menuItem.Tag is not string categoryName)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 无法从Tag获取分类名称，Tag类型: {menuItem.Tag?.GetType().Name ?? "null"}");
                _viewModel.DebugMessage = "无法获取分类名称";
                return;
            }

            System.Diagnostics.Debug.WriteLine($"✅ 获取到分类名称: '{categoryName}'");

            // 通过ContextMenu的PlacementTarget查找软件
            var contextMenu = menuItem.Parent as ContextMenu;
            var placementTarget = contextMenu?.PlacementTarget as Visual;

            System.Diagnostics.Debug.WriteLine($"PlacementTarget类型: {placementTarget?.GetType().Name ?? "null"}");

            if (placementTarget == null)
            {
                System.Diagnostics.Debug.WriteLine("❌ 无法获取ContextMenu的PlacementTarget");
                _viewModel.DebugMessage = "无法确定目标软件";
                return;
            }

            // 向上查找ListBoxItem
            var listBoxItem = placementTarget.FindAncestorOfType<ListBoxItem>();
            if (listBoxItem?.DataContext is SoftwarePackageInfo software)
            {
                System.Diagnostics.Debug.WriteLine($"✅ 通过鼠标位置找到软件: '{software.Name}'");
                System.Diagnostics.Debug.WriteLine($"软件当前分类: [{string.Join(", ", software.Categories)}]");

                if (software.Categories.Contains(categoryName))
                {
                    // 执行删除操作
                    await _viewModel.RemoveCategoryFromSoftwareCommand.ExecuteAsync(new { Software = software, CategoryName = categoryName });
                    _viewModel.DebugMessage = $"✅ 已从软件 '{software.Name}' 中删除分类 '{categoryName}'";
                    System.Diagnostics.Debug.WriteLine($"✅ 成功删除分类");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 软件 '{software.Name}' 中未找到分类 '{categoryName}'");
                    _viewModel.DebugMessage = $"软件 '{software.Name}' 中未找到分类 '{categoryName}'";
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ 无法通过鼠标位置找到软件");
                _viewModel.DebugMessage = "无法确定目标软件";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 删除分类失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
            _viewModel.DebugMessage = $"删除分类失败: {ex.Message}";
        }
    }


}
