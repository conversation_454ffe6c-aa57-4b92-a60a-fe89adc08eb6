using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Threading.Tasks;
using Avalonia;
using LSSOFT.ViewModels;
using LSSOFT.Models;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Avalonia.Controls.Selection;

namespace LSSOFT.Views.Modules;

public partial class SoftwareLocalizationView : UserControl
{
    private SoftwareLocalizationViewModel? _viewModel;

    public SoftwareLocalizationView()
    {
        try
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化开始");

            var databaseService = new LSSOFT.Services.DatabaseService();
            _viewModel = new SoftwareLocalizationViewModel(databaseService);
            this.DataContext = _viewModel;

            // 绑定事件
            if (this.FindControl<Button>("SelectPathButton") is Button btn)
                btn.Click += SelectPathButton_Click;

            // 初始化磁盘选择下拉框
            InitializeDriveComboBox();

            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SoftwareLocalizationView 初始化失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 初始化磁盘选择下拉框
    /// </summary>
    private void InitializeDriveComboBox()
    {
        try
        {
            if (this.FindControl<ComboBox>("DriveComboBox") is ComboBox driveComboBox)
            {
                // 获取所有可用的磁盘驱动器
                var drives = DriveInfo.GetDrives()
                    .Where(d => d.IsReady && d.DriveType == DriveType.Fixed)
                    .Select(d => new DriveDisplayItem
                    {
                        Name = $"{d.Name.TrimEnd('\\')} ({GetDriveLabel(d)})",
                        Path = d.Name
                    })
                    .ToList();

                // 添加一个默认选项
                drives.Insert(0, new DriveDisplayItem { Name = "选择磁盘", Path = "" });

                driveComboBox.ItemsSource = drives;
                driveComboBox.DisplayMemberBinding = new Avalonia.Data.Binding("Name");
                driveComboBox.SelectedIndex = 0;

                // 绑定选择变化事件
                driveComboBox.SelectionChanged += DriveComboBox_SelectionChanged;

                System.Diagnostics.Debug.WriteLine($"初始化了 {drives.Count - 1} 个磁盘驱动器");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"初始化磁盘选择失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取磁盘标签
    /// </summary>
    private string GetDriveLabel(DriveInfo drive)
    {
        try
        {
            return string.IsNullOrEmpty(drive.VolumeLabel) ? "本地磁盘" : drive.VolumeLabel;
        }
        catch
        {
            return "本地磁盘";
        }
    }

    /// <summary>
    /// 磁盘选择变化事件
    /// </summary>
    private void DriveComboBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
    {
        try
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is DriveDisplayItem selectedDrive)
            {
                if (!string.IsNullOrEmpty(selectedDrive.Path) && _viewModel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"用户选择磁盘: {selectedDrive.Path}");
                    _viewModel.ScanPath = selectedDrive.Path;
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"磁盘选择失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 磁盘显示项
    /// </summary>
    private class DriveDisplayItem
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
    }

    private async void SelectPathButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            var window = this.VisualRoot as Window;
            if (window?.StorageProvider != null)
            {
                var folders = await window.StorageProvider.OpenFolderPickerAsync(new Avalonia.Platform.Storage.FolderPickerOpenOptions
                {
                    Title = "选择整理路径",
                    AllowMultiple = false,
                    // 允许选择根目录
                    SuggestedStartLocation = null
                });
                if (folders != null && folders.Count > 0)
                {
                    var path = folders[0].Path.LocalPath;
                    System.Diagnostics.Debug.WriteLine($"用户选择的路径: {path}");

                    // 验证路径是否有效
                    if (System.IO.Directory.Exists(path))
                    {
                        // 更新ViewModel，这会触发UI更新
                        if (_viewModel != null)
                        {
                            _viewModel.ScanPath = path;
                            System.Diagnostics.Debug.WriteLine($"路径已更新到ViewModel: {path}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"选择的路径无效: {path}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"选择路径失败: {ex.Message}");
        }
    }

    private async void OnShowDetailClick(object? sender, RoutedEventArgs e)
    {
        if (sender is Button btn && btn.CommandParameter is SoftwarePackageInfo info)
        {
            var uri = new Uri("avares://LSSOFT/Assets/avalonia-logo.ico");
            Bitmap? iconBitmap = null;
            try
            {
                using var stream = AssetLoader.Open(uri);
                iconBitmap = new Bitmap(stream);
            }
            catch { }
            var stack = new StackPanel { Margin = new Thickness(20), Spacing = 12 };
            if (iconBitmap != null)
                stack.Children.Add(new Image { Source = iconBitmap, Width = 48, Height = 48 });
            stack.Children.Add(new TextBlock { Text = $"软件名称：{info.Name}", FontWeight = Avalonia.Media.FontWeight.Bold, FontSize = 16 });
            stack.Children.Add(new TextBlock { Text = $"文件路径：{info.FilePath}", FontSize = 12, Foreground = Avalonia.Media.Brushes.Gray });
            stack.Children.Add(new TextBlock { Text = $"文件大小：{info.Size}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"版本信息：{info.Version ?? "-"}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"软件描述：{info.Description}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"创建时间：{info.CreatedDate:yyyy-MM-dd HH:mm:ss}", FontSize = 13 });
            var dialog = new Window
            {
                Title = "软件详细信息",
                Width = 400,
                Height = 320,
                Content = stack
            };
            if (this.VisualRoot is Window parent)
                await dialog.ShowDialog(parent);
        }
    }
}
