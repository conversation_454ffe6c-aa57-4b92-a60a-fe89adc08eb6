using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Threading.Tasks;
using Avalonia;
using LSSOFT.ViewModels;
using LSSOFT.Models;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using System;
using System.IO;

namespace LSSOFT.Views.Modules;

public partial class SoftwareLocalizationView : UserControl
{
    private SoftwareLocalizationViewModel? _viewModel;

    public SoftwareLocalizationView()
    {
        try
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化开始");

            var databaseService = new LSSOFT.Services.DatabaseService();
            _viewModel = new SoftwareLocalizationViewModel(databaseService);
            this.DataContext = _viewModel;

            // 绑定事件
            if (this.FindControl<Button>("SelectPathButton") is Button btn)
                btn.Click += SelectPathButton_Click;

            // 绑定路径输入框事件，支持手动输入磁盘根目录
            if (this.FindControl<TextBox>("PathTextBox") is TextBox pathTextBox)
            {
                pathTextBox.KeyDown += PathTextBox_KeyDown;
            }

            // 添加测试按钮来验证路径切换
            AddTestButtons();

            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView 初始化完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SoftwareLocalizationView 初始化失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 添加测试按钮来验证路径切换功能
    /// </summary>
    private void AddTestButtons()
    {
        try
        {
            // 创建测试按钮
            var testButton1 = new Button
            {
                Content = "测试C:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton1.Click += (s, e) => TestPathChange("C:\\");

            var testButton2 = new Button
            {
                Content = "测试D:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton2.Click += (s, e) => TestPathChange("D:\\");

            var testButton3 = new Button
            {
                Content = "测试E:\\",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testButton3.Click += (s, e) => TestPathChange("E:\\");

            var testSelectButton = new Button
            {
                Content = "测试选择",
                Margin = new Thickness(5),
                Width = 80,
                Height = 30
            };
            testSelectButton.Click += (s, e) => TestSelectPath();

            // 将测试按钮添加到界面（临时用于调试）
            System.Diagnostics.Debug.WriteLine("测试按钮已创建，可以通过代码调用TestPathChange方法");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加测试按钮失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试路径切换
    /// </summary>
    private void TestPathChange(string path)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换到: {path}");
            if (_viewModel != null && Directory.Exists(path))
            {
                System.Diagnostics.Debug.WriteLine($"当前路径: {_viewModel.ScanPath}");

                // 使用强制切换方法
                _ = Task.Run(async () =>
                {
                    await _viewModel.ChangeScanPathAsync(path);
                });

                System.Diagnostics.Debug.WriteLine($"测试路径切换处理完成: {path}");
            }
            else
            {
                if (_viewModel == null)
                    System.Diagnostics.Debug.WriteLine("ViewModel为空，无法测试");
                if (!Directory.Exists(path))
                    System.Diagnostics.Debug.WriteLine($"测试路径不存在: {path}");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试路径切换失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试选择路径功能
    /// </summary>
    private void TestSelectPath()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("开始测试选择路径功能...");

            // 直接调用选择路径按钮的逻辑
            SelectPathButton_Click(null, new RoutedEventArgs());

            System.Diagnostics.Debug.WriteLine("测试选择路径功能完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"测试选择路径功能失败: {ex.Message}");
        }
    }



    /// <summary>
    /// 路径输入框按键事件，支持快速输入磁盘根目录
    /// </summary>
    private void PathTextBox_KeyDown(object? sender, Avalonia.Input.KeyEventArgs e)
    {
        try
        {
            if (e.Key == Avalonia.Input.Key.Enter && sender is TextBox textBox)
            {
                var inputPath = textBox.Text?.Trim();
                if (!string.IsNullOrEmpty(inputPath))
                {
                    // 自动补全磁盘根目录格式
                    if (inputPath.Length == 1 && char.IsLetter(inputPath[0]))
                    {
                        // 输入单个字母，自动补全为磁盘根目录
                        inputPath = $"{inputPath.ToUpper()}:\\";
                        textBox.Text = inputPath;
                    }
                    else if (inputPath.Length == 2 && char.IsLetter(inputPath[0]) && inputPath[1] == ':')
                    {
                        // 输入"C:"格式，自动补全为"C:\"
                        inputPath = $"{inputPath}\\";
                        textBox.Text = inputPath;
                    }

                    // 验证路径并更新ViewModel
                    System.Diagnostics.Debug.WriteLine($"准备验证路径: {inputPath}");
                    System.Diagnostics.Debug.WriteLine($"路径是否存在: {Directory.Exists(inputPath)}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel是否为空: {_viewModel == null}");

                    if (Directory.Exists(inputPath) && _viewModel != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"手动输入路径: {inputPath}");

                        // 先更新TextBox显示
                        textBox.Text = inputPath;

                        // 使用强制切换方法
                        _ = Task.Run(async () =>
                        {
                            await _viewModel.ChangeScanPathAsync(inputPath);
                        });

                        System.Diagnostics.Debug.WriteLine($"手动输入路径处理完成: {inputPath}");
                    }
                    else
                    {
                        if (!Directory.Exists(inputPath))
                            System.Diagnostics.Debug.WriteLine($"路径不存在: {inputPath}");
                        if (_viewModel == null)
                            System.Diagnostics.Debug.WriteLine("ViewModel为空");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"路径输入处理失败: {ex.Message}");
        }
    }

    private async void SelectPathButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            var window = this.VisualRoot as Window;
            if (window?.StorageProvider != null)
            {
                var folders = await window.StorageProvider.OpenFolderPickerAsync(new Avalonia.Platform.Storage.FolderPickerOpenOptions
                {
                    Title = "选择整理路径 - 支持选择磁盘根目录",
                    AllowMultiple = false
                });
                if (folders != null && folders.Count > 0)
                {
                    var path = folders[0].Path.LocalPath;
                    System.Diagnostics.Debug.WriteLine($"用户选择的路径: {path}");
                    System.Diagnostics.Debug.WriteLine($"路径类型: {folders[0].Path.GetType()}");
                    System.Diagnostics.Debug.WriteLine($"路径Scheme: {folders[0].Path.Scheme}");
                    System.Diagnostics.Debug.WriteLine($"路径AbsolutePath: {folders[0].Path.AbsolutePath}");

                    // 验证路径是否有效
                    System.Diagnostics.Debug.WriteLine($"准备验证选择的路径: {path}");
                    System.Diagnostics.Debug.WriteLine($"选择的路径是否存在: {Directory.Exists(path)}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel是否为空: {_viewModel == null}");

                    if (Directory.Exists(path))
                    {
                        // 更新ViewModel，这会触发UI更新
                        if (_viewModel != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"选择路径: {path}");

                            // 先更新TextBox显示
                            if (this.FindControl<TextBox>("PathTextBox") is TextBox pathTextBox)
                            {
                                pathTextBox.Text = path;
                                System.Diagnostics.Debug.WriteLine($"已更新TextBox显示: {path}");
                            }

                            // 使用强制切换方法
                            _ = Task.Run(async () =>
                            {
                                System.Diagnostics.Debug.WriteLine($"开始强制切换路径: {path}");
                                await _viewModel.ChangeScanPathAsync(path);
                                System.Diagnostics.Debug.WriteLine($"强制切换路径完成: {path}");
                            });

                            System.Diagnostics.Debug.WriteLine($"选择路径处理完成: {path}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("ViewModel为空，无法更新路径");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"选择的路径无效: {path}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"选择路径失败: {ex.Message}");
        }
    }

    private async void OnShowDetailClick(object? sender, RoutedEventArgs e)
    {
        if (sender is Button btn && btn.CommandParameter is SoftwarePackageInfo info)
        {
            var uri = new Uri("avares://LSSOFT/Assets/avalonia-logo.ico");
            Bitmap? iconBitmap = null;
            try
            {
                using var stream = AssetLoader.Open(uri);
                iconBitmap = new Bitmap(stream);
            }
            catch { }
            var stack = new StackPanel { Margin = new Thickness(20), Spacing = 12 };
            if (iconBitmap != null)
                stack.Children.Add(new Image { Source = iconBitmap, Width = 48, Height = 48 });
            stack.Children.Add(new TextBlock { Text = $"软件名称：{info.Name}", FontWeight = Avalonia.Media.FontWeight.Bold, FontSize = 16 });
            stack.Children.Add(new TextBlock { Text = $"文件路径：{info.FilePath}", FontSize = 12, Foreground = Avalonia.Media.Brushes.Gray });
            stack.Children.Add(new TextBlock { Text = $"文件大小：{info.Size}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"版本信息：{info.Version ?? "-"}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"软件描述：{info.Description}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"创建时间：{info.CreatedDate:yyyy-MM-dd HH:mm:ss}", FontSize = 13 });
            var dialog = new Window
            {
                Title = "软件详细信息",
                Width = 400,
                Height = 320,
                Content = stack
            };
            if (this.VisualRoot is Window parent)
                await dialog.ShowDialog(parent);
        }
    }
}
