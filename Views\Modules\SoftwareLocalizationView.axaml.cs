using Avalonia.Controls;
using Avalonia.Interactivity;
using System.Threading.Tasks;
using Avalonia;
using LSSOFT.ViewModels;
using LSSOFT.Models;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using System;
using System.Collections.Generic;

namespace LSSOFT.Views.Modules;

public partial class SoftwareLocalizationView : UserControl
{
    private SoftwareLocalizationViewModel? _viewModel;

    public SoftwareLocalizationView()
    {
        try
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView ????????");

            var databaseService = new LSSOFT.Services.DatabaseService();
            _viewModel = new SoftwareLocalizationViewModel(databaseService);
            this.DataContext = _viewModel;

            // ?????
            if (this.FindControl<Button>("SelectPathButton") is Button btn)
                btn.Click += SelectPathButton_Click;

            System.Diagnostics.Debug.WriteLine("SoftwareLocalizationView ????????");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SoftwareLocalizationView ????????: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"??????: {ex}");
        }
    }

    private async void SelectPathButton_Click(object? sender, RoutedEventArgs e)
    {
        try
        {
            var window = this.VisualRoot as Window;
            if (window?.StorageProvider != null)
            {
                var folders = await window.StorageProvider.OpenFolderPickerAsync(new Avalonia.Platform.Storage.FolderPickerOpenOptions
                {
                    Title = "???????¡¤??",
                    AllowMultiple = false
                });
                if (folders != null && folders.Count > 0)
                {
                    var path = folders[0].Path.LocalPath;
                    System.Diagnostics.Debug.WriteLine($"????????¡¤??: {path}");
                    // ?????ViewModel?????????UI????
                    if (_viewModel != null)
                        _viewModel.ScanPath = path;
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"???¡¤?????: {ex.Message}");
        }
    }

    private async void OnShowDetailClick(object? sender, RoutedEventArgs e)
    {
        if (sender is Button btn && btn.CommandParameter is SoftwarePackageInfo info)
        {
            var uri = new Uri("avares://LSSOFT/Assets/avalonia-logo.ico");
            Bitmap? iconBitmap = null;
            try
            {
                using var stream = AssetLoader.Open(uri);
                iconBitmap = new Bitmap(stream);
            }
            catch { }
            var stack = new StackPanel { Margin = new Thickness(20), Spacing = 12 };
            if (iconBitmap != null)
                stack.Children.Add(new Image { Source = iconBitmap, Width = 48, Height = 48 });
            stack.Children.Add(new TextBlock { Text = $"???????{info.Name}", FontWeight = Avalonia.Media.FontWeight.Bold, FontSize = 16 });
            stack.Children.Add(new TextBlock { Text = $"¡¤????{info.FilePath}", FontSize = 12, Foreground = Avalonia.Media.Brushes.Gray });
            stack.Children.Add(new TextBlock { Text = $"??§³??{info.Size}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"?·Ú???{info.Version ?? "-"}", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"????????¦Ä????", FontSize = 13 });
            stack.Children.Add(new TextBlock { Text = $"???????{info.CreatedDate:yyyy-MM-dd HH:mm:ss}", FontSize = 13 });
            var dialog = new Window
            {
                Title = "?????????",
                Width = 400,
                Height = 320,
                Content = stack
            };
            if (this.VisualRoot is Window parent)
                await dialog.ShowDialog(parent);
        }
    }
}
