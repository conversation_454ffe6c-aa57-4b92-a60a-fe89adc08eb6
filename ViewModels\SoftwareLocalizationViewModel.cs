using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Models;
using LSSOFT.Services;

namespace LSSOFT.ViewModels;

public partial class SoftwareLocalizationViewModel : ViewModelBase
{
    private readonly DatabaseService _databaseService;
    private readonly ShortcutService _shortcutService;

    /// <summary>
    /// 快捷方式服务（用于测试）
    /// </summary>
    public ShortcutService ShortcutService => _shortcutService;

    private string _scanPath = string.Empty;

    /// <summary>
    /// 扫描路径属性
    /// </summary>
    public string ScanPath
    {
        get => _scanPath;
        set
        {
            var oldValue = _scanPath;
            if (SetProperty(ref _scanPath, value))
            {
                System.Diagnostics.Debug.WriteLine($"ScanPath属性变化: '{oldValue}' -> '{value}'");
                OnScanPathChanged(value);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"ScanPath属性未变化，保持: '{value}'");
            }
        }
    }

    /// <summary>
    /// 强制切换扫描路径并立即扫描
    /// </summary>
    public async Task ChangeScanPathAsync(string newPath)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"强制切换路径: '{_scanPath}' -> '{newPath}'");
            // 只用属性赋值，自动触发所有逻辑
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                ScanPath = newPath;
            });
            // 不需要再手动触发扫描，ScanPath 的 set 已经会自动处理
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"强制切换路径失败: {ex.Message}");
        }
    }

    [ObservableProperty]
    private ObservableCollection<SoftwarePackageInfo> _localPackages = new();

    [ObservableProperty]
    private ObservableCollection<SoftwareCategoryInfo> _categories = new();

    [ObservableProperty]
    private bool _isAddCategoryDialogVisible = false;

    [ObservableProperty]
    private string _newCategoryName = string.Empty;

    [ObservableProperty]
    private bool _isEditCategoryDialogVisible = false;

    [ObservableProperty]
    private string _editCategoryName = string.Empty;

    [ObservableProperty]
    private string _originalCategoryName = string.Empty;

    [ObservableProperty]
    private string _draggedCategoryName = string.Empty;

    [ObservableProperty]
    private string _scanStatus = "就绪";

    [ObservableProperty]
    private bool _isScanning = false;

    [ObservableProperty]
    private string _debugMessage = "系统就绪";

    private CancellationTokenSource? _scanCts;
    private CancellationTokenSource? _pathChangeCts;
    private bool _isInitialized = false;

    public SoftwareLocalizationViewModel(DatabaseService databaseService, ShortcutService shortcutService)
    {
        _databaseService = databaseService;
        _shortcutService = shortcutService;

        // 异步初始化
        _ = Task.Run(InitializeAsync);
    }

    /// <summary>
    /// 异步初始化
    /// </summary>
    private async Task InitializeAsync()
    {
        if (_isInitialized)
        {
            System.Diagnostics.Debug.WriteLine("已经初始化过，跳过重复初始化");
            return;
        }

        try
        {
            // 测试状态显示
            ScanStatus = "正在初始化...";
            System.Diagnostics.Debug.WriteLine($"初始化时设置状态: {ScanStatus}");

            // 从数据库加载默认路径
            var defaultPath = await _databaseService.GetDefaultOrganizationPathAsync();
            ScanPath = defaultPath;

            // 初始化分类
            await InitializeCategoriesAsync();

            // 标记为已初始化
            _isInitialized = true;

            // 等待一下再开始扫描，确保界面已经准备好
            await Task.Delay(500); // 让UI有时间更新
            await ScanAndLoadPackagesAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"初始化失败: {ex.Message}");
            // 使用默认路径
            ScanPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + @"\Downloads";
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                InitializeCategories();
            });
            _isInitialized = true;
        }
    }

    /// <summary>
    /// 异步初始化分类
    /// </summary>
    private async Task InitializeCategoriesAsync()
    {
        try
        {
            var categories = await _databaseService.GetCategoriesAsync();

            // 确保清空现有分类，避免重复
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                Categories.Clear();
            });

            foreach (var category in categories)
            {
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    Categories.Add(new SoftwareCategoryInfo(category.Name) { Count = category.Count });
                });
            }

            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                if (Categories.Count > 0)
                    Categories[0].IsSelected = true;
            });

            System.Diagnostics.Debug.WriteLine($"从数据库加载了 {Categories.Count} 个分类");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载分类失败: {ex.Message}");
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                InitializeCategories();
            });
        }
    }

    /// <summary>
    /// 初始化默认分类（备用方案）
    /// </summary>
    private void InitializeCategories()
    {
        Categories.Clear();
        Categories.Add(new SoftwareCategoryInfo("全部"));
        Categories.Add(new SoftwareCategoryInfo("系统"));
        Categories.Add(new SoftwareCategoryInfo("办公"));
        Categories.Add(new SoftwareCategoryInfo("开发"));
        Categories.Add(new SoftwareCategoryInfo("设计"));
        Categories.Add(new SoftwareCategoryInfo("游戏"));
        Categories.Add(new SoftwareCategoryInfo("手机"));
        Categories.Add(new SoftwareCategoryInfo("其它"));
        if (Categories.Count > 0)
            Categories[0].IsSelected = true;
    }

    [RelayCommand]
    public async Task ScanAndLoadPackagesAsync()
    {
        try
        {
            // 取消之前的扫描
            _scanCts?.Cancel();
            var cts = new CancellationTokenSource();
            _scanCts = cts;
            var token = cts.Token;

            var scanPath = ScanPath;
            System.Diagnostics.Debug.WriteLine($"开始扫描路径: {scanPath}");

            // 更新UI状态
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                IsScanning = true;
                ScanStatus = "正在扫描...";
            });

            // 验证路径是否存在
            if (string.IsNullOrEmpty(scanPath))
            {
                System.Diagnostics.Debug.WriteLine("扫描路径为空");
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    LocalPackages.Clear();
                    ScanStatus = "路径为空";
                    IsScanning = false;
                });
                return;
            }

            if (!Directory.Exists(scanPath))
            {
                System.Diagnostics.Debug.WriteLine($"路径不存在: {scanPath}");
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    LocalPackages.Clear();
                    ScanStatus = $"路径不存在: {scanPath}";
                    IsScanning = false;
                });
                return;
            }

            System.Diagnostics.Debug.WriteLine($"路径验证通过: {scanPath}");

            // 在后台线程执行文件扫描
            var fileList = await Task.Run(async () =>
            {
                try
                {
                    var exts = new[] { ".exe", ".msi", ".zip", ".rar", ".7z", ".tar", ".gz" };
                    var files = new List<FileInfo>();

                    // 检查是否为根目录（如 C:\, D:\ 等）
                    bool isRootDirectory = IsRootDirectory(scanPath);
                    System.Diagnostics.Debug.WriteLine($"扫描路径: {scanPath}, 是否为根目录: {isRootDirectory}");

                    // 添加更详细的路径信息
                    System.Diagnostics.Debug.WriteLine($"路径存在检查: {Directory.Exists(scanPath)}");
                    if (Directory.Exists(scanPath))
                    {
                        try
                        {
                            var dirInfo = new DirectoryInfo(scanPath);
                            System.Diagnostics.Debug.WriteLine($"路径详情 - 全路径: {dirInfo.FullName}, 父目录: {dirInfo.Parent?.FullName ?? "null"}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"获取路径详情失败: {ex.Message}");
                        }
                    }

                    if (isRootDirectory)
                    {
                        // 根目录扫描：使用特殊处理
                        System.Diagnostics.Debug.WriteLine("使用根目录扫描策略");

                        // 更新状态显示正在扫描根目录
                        await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                        {
                            ScanStatus = "正在扫描根目录...";
                        });

                        files = ScanRootDirectory(scanPath, exts);
                    }
                    else
                    {
                        // 普通目录扫描
                        System.Diagnostics.Debug.WriteLine("使用普通目录扫描策略");
                        files = Directory.GetFiles(scanPath, "*.*", SearchOption.TopDirectoryOnly)
                            .Where(f => exts.Contains(Path.GetExtension(f).ToLowerInvariant()))
                            .Take(1000) // 限制最大文件数量，避免过多文件导致卡顿
                            .Select(f => new FileInfo(f))
                            .ToList();
                    }

                    System.Diagnostics.Debug.WriteLine($"找到 {files.Count} 个安装包文件");
                    return files;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"扫描文件失败: {ex.Message}");
                    return new List<FileInfo>();
                }
            }, token);

            token.ThrowIfCancellationRequested();

            // 转换为显示对象
            var result = new List<SoftwarePackageInfo>();
            foreach (var fi in fileList)
            {
                var iconPath = await _shortcutService.ExtractApplicationIconAsync(fi.FullName);
                var category = await _databaseService.GetSoftwareCategoryAsync(fi.FullName);
                var categories = await _databaseService.GetSoftwareCategoriesAsync(fi.FullName);

                // 提取版本信息
                var version = ExtractFileVersion(fi.FullName);

                var software = new SoftwarePackageInfo
                {
                    Name = fi.Name,
                    FilePath = fi.FullName,
                    IconPath = iconPath ?? string.Empty,
                    Size = FormatFileSize(fi.Length),
                    CreatedDate = fi.CreationTime,
                    Description = fi.FullName,
                    Content = fi.FullName,
                    License = "本地文件",
                    Stars = "-",
                    Downloads = "-",
                    SupportDonation = false,
                    SupportAI = false,
                    Version = version,
                    Category = category
                };

                // 添加所有分类到Categories集合
                foreach (var cat in categories)
                {
                    software.Categories.Add(cat);
                }

                result.Add(software);
            }

            // 在UI线程更新集合
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                LocalPackages.Clear();
                foreach (var pkg in result)
                {
                    LocalPackages.Add(pkg);
                }

                // 更新状态
                ScanStatus = $"找到 {LocalPackages.Count} 个安装包";
                IsScanning = false;
                System.Diagnostics.Debug.WriteLine($"已加载 {LocalPackages.Count} 个安装包到界面");
            });

            // 更新分类统计数字
            UpdateCategoryCounts();
        }
        catch (OperationCanceledException)
        {
            System.Diagnostics.Debug.WriteLine("扫描操作被取消");
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                ScanStatus = "扫描已取消";
                IsScanning = false;
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"扫描失败: {ex.Message}");
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                LocalPackages.Clear();
                ScanStatus = $"扫描失败: {ex.Message}";
                IsScanning = false;
            });
        }
    }

    /// <summary>
    /// 检查是否为根目录
    /// </summary>
    private bool IsRootDirectory(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path))
                return false;

            // 标准化路径
            path = Path.GetFullPath(path);

            // 检查是否为驱动器根目录 (如 C:\, D:\)
            var directoryInfo = new DirectoryInfo(path);
            bool isRoot = directoryInfo.Parent == null;

            System.Diagnostics.Debug.WriteLine($"路径检测: {path} -> 是否为根目录: {isRoot}");
            return isRoot;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"根目录检测失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 扫描根目录，使用特殊策略避免性能问题
    /// </summary>
    private List<FileInfo> ScanRootDirectory(string rootPath, string[] extensions)
    {
        var files = new List<FileInfo>();

        try
        {
            System.Diagnostics.Debug.WriteLine($"开始扫描根目录: {rootPath}");

            // 首先尝试直接扫描根目录
            try
            {
                var rootFiles = Directory.GetFiles(rootPath, "*.*", SearchOption.TopDirectoryOnly)
                    .Where(f => extensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                    .Take(200) // 根目录限制文件数量
                    .Select(f => new FileInfo(f))
                    .ToList();

                files.AddRange(rootFiles);
                System.Diagnostics.Debug.WriteLine($"根目录直接扫描找到 {rootFiles.Count} 个文件");
            }
            catch (UnauthorizedAccessException)
            {
                System.Diagnostics.Debug.WriteLine("根目录访问权限不足，跳过直接扫描");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"根目录直接扫描失败: {ex.Message}");
            }

            // 扫描根目录下的所有文件夹（但有限制）
            try
            {
                var allDirectories = Directory.GetDirectories(rootPath);
                System.Diagnostics.Debug.WriteLine($"根目录下共有 {allDirectories.Length} 个文件夹");

                // 优先扫描常见的软件安装文件夹
                var priorityFolders = new[] {
                    "Downloads", "下载", "Desktop", "桌面", "Documents", "文档",
                    "Program Files", "Program Files (x86)", "软件", "Software",
                    "Tools", "工具", "Apps", "应用", "Setup", "安装包", "Installer"
                };

                var priorityDirs = allDirectories
                    .Where(d =>
                    {
                        var dirName = Path.GetFileName(d);
                        return priorityFolders.Any(pf => dirName.Contains(pf, StringComparison.OrdinalIgnoreCase));
                    })
                    .Take(5); // 优先文件夹限制5个

                // 扫描优先文件夹
                foreach (var dir in priorityDirs)
                {
                    try
                    {
                        var dirFiles = Directory.GetFiles(dir, "*.*", SearchOption.TopDirectoryOnly)
                            .Where(f => extensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                            .Take(50) // 每个优先文件夹限制50个文件
                            .Select(f => new FileInfo(f))
                            .ToList();

                        files.AddRange(dirFiles);
                        System.Diagnostics.Debug.WriteLine($"优先文件夹 {dir} 找到 {dirFiles.Count} 个文件");

                        if (files.Count >= 300) break; // 总数限制
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"扫描优先文件夹 {dir} 失败: {ex.Message}");
                    }
                }

                // 如果优先文件夹没找到足够文件，扫描其他文件夹
                if (files.Count < 50)
                {
                    var otherDirs = allDirectories
                        .Where(d => !priorityDirs.Contains(d))
                        .Where(d =>
                        {
                            var dirName = Path.GetFileName(d);
                            // 排除系统文件夹
                            var excludeFolders = new[] {
                                "Windows", "System", "Boot", "$Recycle.Bin", "Recovery",
                                "PerfLogs", "Config.Msi", "MSOCache", "System Volume Information",
                                "hiberfil.sys", "pagefile.sys", "swapfile.sys"
                            };
                            return !excludeFolders.Any(ef => dirName.Equals(ef, StringComparison.OrdinalIgnoreCase));
                        })
                        .Take(10); // 其他文件夹限制10个

                    foreach (var dir in otherDirs)
                    {
                        try
                        {
                            var dirFiles = Directory.GetFiles(dir, "*.*", SearchOption.TopDirectoryOnly)
                                .Where(f => extensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                                .Take(20) // 每个其他文件夹限制20个文件
                                .Select(f => new FileInfo(f))
                                .ToList();

                            files.AddRange(dirFiles);
                            System.Diagnostics.Debug.WriteLine($"其他文件夹 {dir} 找到 {dirFiles.Count} 个文件");

                            if (files.Count >= 300) break; // 总数限制
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"扫描其他文件夹 {dir} 失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取根目录子文件夹失败: {ex.Message}");
            }

            System.Diagnostics.Debug.WriteLine($"根目录扫描完成，总共找到 {files.Count} 个文件");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"根目录扫描完全失败: {ex.Message}");
        }

        return files;
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes >= 1024 * 1024 * 1024)
            return $"{bytes / 1024.0 / 1024.0 / 1024.0:F1} GB";
        else if (bytes >= 1024 * 1024)
            return $"{bytes / 1024.0 / 1024.0:F1} MB";
        else if (bytes >= 1024)
            return $"{bytes / 1024.0:F1} KB";
        else
            return $"{bytes} B";
    }

    private void OnScanPathChanged(string value)
    {
        System.Diagnostics.Debug.WriteLine($"OnScanPathChanged 被调用: {value}");

        // 更新调试信息
        DebugMessage = $"路径变化: {value} | 时间: {DateTime.Now:HH:mm:ss}";

        // 取消之前的路径变化处理
        _pathChangeCts?.Cancel();
        var cts = new CancellationTokenSource();
        _pathChangeCts = cts;

        // 立即更新UI状态
        _ = Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            ScanStatus = "路径已更改，准备扫描...";
            IsScanning = true;
            System.Diagnostics.Debug.WriteLine($"UI状态已更新: ScanStatus={ScanStatus}, IsScanning={IsScanning}");
        });

        // 保存路径到数据库并延迟扫描
        _ = Task.Run(async () =>
        {
            try
            {
                // 检查是否被取消
                if (cts.Token.IsCancellationRequested)
                {
                    System.Diagnostics.Debug.WriteLine("路径变化处理被取消");
                    return;
                }

                // 保存新路径到数据库
                if (!string.IsNullOrEmpty(value))
                {
                    System.Diagnostics.Debug.WriteLine($"保存路径到数据库: {value}");
                    await _databaseService.SetDefaultOrganizationPathAsync(value);
                }

                // 防抖动延迟
                await Task.Delay(300, cts.Token);

                if (cts.Token.IsCancellationRequested)
                {
                    System.Diagnostics.Debug.WriteLine("路径变化处理在延迟后被取消");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"开始扫描新路径: {value}");
                await ScanAndLoadPackagesAsync();
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("路径变化处理被取消");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径变化处理失败: {ex.Message}");
                // 即使保存失败，也继续扫描
                try
                {
                    await Task.Delay(300, cts.Token);
                    if (!cts.Token.IsCancellationRequested)
                    {
                        await ScanAndLoadPackagesAsync();
                    }
                }
                catch (OperationCanceledException)
                {
                    System.Diagnostics.Debug.WriteLine("路径变化处理在异常恢复时被取消");
                }
            }
        }, cts.Token);
    }

    [RelayCommand]
    private void ShowAddCategoryDialog()
    {
        NewCategoryName = string.Empty;
        IsAddCategoryDialogVisible = true;
    }

    [RelayCommand]
    private async Task AddCategory()
    {
        if (string.IsNullOrWhiteSpace(NewCategoryName))
            return;

        // 检查是否已存在
        if (Categories.Any(c => c.Name == NewCategoryName))
            return;

        try
        {
            // 添加到数据库
            await _databaseService.AddCategoryAsync(NewCategoryName);

            // 添加到UI
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                Categories.Add(new SoftwareCategoryInfo(NewCategoryName) { Count = 0 });
            });

            IsAddCategoryDialogVisible = false;
            NewCategoryName = string.Empty;

            System.Diagnostics.Debug.WriteLine($"成功添加分类: {NewCategoryName}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"添加分类失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private void CancelAddCategory()
    {
        IsAddCategoryDialogVisible = false;
        NewCategoryName = string.Empty;
    }

    [RelayCommand]
    private void ShowEditCategoryDialog(string categoryName)
    {
        OriginalCategoryName = categoryName;
        EditCategoryName = categoryName;
        IsEditCategoryDialogVisible = true;
    }

    [RelayCommand]
    private async Task EditCategory()
    {
        if (string.IsNullOrWhiteSpace(EditCategoryName))
            return;

        if (EditCategoryName == OriginalCategoryName)
        {
            IsEditCategoryDialogVisible = false;
            return;
        }

        // 检查新名称是否已存在
        if (Categories.Any(c => c.Name == EditCategoryName))
        {
            System.Diagnostics.Debug.WriteLine($"分类名称已存在: {EditCategoryName}");
            return;
        }

        try
        {
            await _databaseService.UpdateCategoryNameAsync(OriginalCategoryName, EditCategoryName);

            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                var category = Categories.FirstOrDefault(c => c.Name == OriginalCategoryName);
                if (category != null)
                {
                    category.Name = EditCategoryName;
                    // 强制触发属性变更通知
                    OnPropertyChanged(nameof(Categories));
                }
            });

            IsEditCategoryDialogVisible = false;
            EditCategoryName = string.Empty;
            OriginalCategoryName = string.Empty;

            System.Diagnostics.Debug.WriteLine($"已修改分类: {OriginalCategoryName} -> {EditCategoryName}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"修改分类失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private void CancelEditCategory()
    {
        IsEditCategoryDialogVisible = false;
        EditCategoryName = string.Empty;
        OriginalCategoryName = string.Empty;
    }

    [RelayCommand]
    private async Task DeleteCategory(string categoryName)
    {
        // 不允许删除"全部"分类，因为它是特殊的筛选分类
        if (categoryName == "全部")
        {
            System.Diagnostics.Debug.WriteLine($"不允许删除'全部'分类");
            return;
        }

        try
        {
            await _databaseService.DeleteCategoryAsync(categoryName);

            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                var category = Categories.FirstOrDefault(c => c.Name == categoryName);
                if (category != null)
                {
                    Categories.Remove(category);
                }
            });

            System.Diagnostics.Debug.WriteLine($"已删除分类: {categoryName}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除分类失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task AssignSoftwareCategory(object parameter)
    {
        if (parameter is not SoftwarePackageInfo software)
            return;

        if (string.IsNullOrEmpty(DraggedCategoryName))
            return;

        try
        {
            await _databaseService.AssignSoftwareCategoryAsync(software.FilePath, DraggedCategoryName);

            // 在UI线程更新软件的分类信息
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(async () =>
            {
                // 重新获取所有分类
                var categories = await _databaseService.GetSoftwareCategoriesAsync(software.FilePath);

                // 更新主分类（显示第一个分类）
                software.Category = categories.FirstOrDefault() ?? "未分类";

                // 更新分类集合
                software.Categories.Clear();
                foreach (var cat in categories)
                {
                    software.Categories.Add(cat);
                }

                // 触发属性更改通知
                OnPropertyChanged(nameof(LocalPackages));
            });

            // 更新分类统计数字
            UpdateCategoryCounts();

            DebugMessage = $"已为软件 '{software.Name}' 添加分类: {DraggedCategoryName}";
            System.Diagnostics.Debug.WriteLine($"已为软件 {software.Name} 分配分类: {DraggedCategoryName}");
            DraggedCategoryName = string.Empty;
        }
        catch (Exception ex)
        {
            DebugMessage = $"分配软件分类失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"分配软件分类失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task FilterByCategory(string categoryName)
    {
        System.Diagnostics.Debug.WriteLine($"分类筛选: {categoryName}");

        // 更新分类选中状态
        foreach (var category in Categories)
        {
            category.IsSelected = category.Name == categoryName;
        }

        // 触发属性更改通知
        OnPropertyChanged(nameof(Categories));

        // 实现分类筛选逻辑
        await ApplyFilterAsync(categoryName);
    }

    /// <summary>
    /// 应用分类筛选
    /// </summary>
    private async Task ApplyFilterAsync(string categoryName)
    {
        try
        {
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                if (categoryName == "全部")
                {
                    // 显示所有软件包
                    foreach (var package in LocalPackages)
                    {
                        package.IsVisible = true;
                    }
                    System.Diagnostics.Debug.WriteLine($"显示所有软件包，共 {LocalPackages.Count} 个");
                }
                else
                {
                    // 根据分类筛选
                    int visibleCount = 0;
                    foreach (var package in LocalPackages)
                    {
                        // 检查软件包是否包含指定分类
                        bool hasCategory = package.Categories.Contains(categoryName) ||
                                         package.Category == categoryName;
                        package.IsVisible = hasCategory;
                        if (hasCategory) visibleCount++;
                    }
                    System.Diagnostics.Debug.WriteLine($"筛选分类 '{categoryName}'，显示 {visibleCount} 个软件包");
                }

                // 触发列表更新
                OnPropertyChanged(nameof(LocalPackages));
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"应用筛选失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新分类统计数字
    /// </summary>
    private void UpdateCategoryCounts()
    {
        try
        {
            foreach (var category in Categories)
            {
                if (category.Name == "全部")
                {
                    // "全部"分类显示所有软件数量
                    category.Count = LocalPackages.Count;
                }
                else
                {
                    // 其他分类显示对应分类的软件数量
                    var count = 0;
                    foreach (var software in LocalPackages)
                    {
                        if (software.Categories.Contains(category.Name))
                        {
                            count++;
                        }
                    }
                    category.Count = count;
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新分类统计失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task RemoveCategoryFromSoftware(object parameter)
    {
        if (parameter is not object paramObj)
            return;

        // 使用反射获取参数
        var softwareProperty = paramObj.GetType().GetProperty("Software");
        var categoryNameProperty = paramObj.GetType().GetProperty("CategoryName");

        if (softwareProperty?.GetValue(paramObj) is not SoftwarePackageInfo software ||
            categoryNameProperty?.GetValue(paramObj) is not string categoryName)
            return;

        try
        {
            // 从数据库中删除分类关联
            await _databaseService.RemoveSoftwareCategoryAsync(software.FilePath, categoryName);

            // 在UI线程更新软件的分类信息
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(async () =>
            {
                // 重新获取所有分类
                var categories = await _databaseService.GetSoftwareCategoriesAsync(software.FilePath);

                // 更新主分类（显示第一个分类）
                software.Category = categories.FirstOrDefault() ?? "未分类";

                // 更新分类集合
                software.Categories.Clear();
                foreach (var cat in categories)
                {
                    software.Categories.Add(cat);
                }

                // 触发属性更改通知
                OnPropertyChanged(nameof(LocalPackages));
            });

            // 更新分类统计数字
            UpdateCategoryCounts();

            DebugMessage = $"已从软件 '{software.Name}' 中删除分类: {categoryName}";
            System.Diagnostics.Debug.WriteLine($"已从软件 {software.Name} 中删除分类: {categoryName}");
        }
        catch (Exception ex)
        {
            DebugMessage = $"删除软件分类失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"删除软件分类失败: {ex.Message}");
        }
    }

    public void SetDebugMessageSafe(string message)
    {
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            DebugMessage = message;
        });
    }

    /// <summary>
    /// 提取文件版本信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>版本信息字符串</returns>
    private string ExtractFileVersion(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return string.Empty;

            // 只处理 .exe 文件
            if (!filePath.EndsWith(".exe", StringComparison.OrdinalIgnoreCase))
                return string.Empty;

            // 使用 System.Diagnostics.FileVersionInfo 获取版本信息
            var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(filePath);

            // 优先使用 ProductVersion，如果为空则使用 FileVersion
            var version = !string.IsNullOrEmpty(versionInfo.ProductVersion)
                ? versionInfo.ProductVersion
                : versionInfo.FileVersion;

            // 清理版本字符串，移除多余的空格和特殊字符
            if (!string.IsNullOrEmpty(version))
            {
                version = version.Trim();
                // 移除常见的版本后缀
                if (version.Contains(" "))
                {
                    // 如果包含空格，只取第一部分（通常是版本号）
                    version = version.Split(' ')[0];
                }
            }

            return version ?? string.Empty;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"提取文件版本失败 {filePath}: {ex.Message}");
            return string.Empty;
        }
    }
}
