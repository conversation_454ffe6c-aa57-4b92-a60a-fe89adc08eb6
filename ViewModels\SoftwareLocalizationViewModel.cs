using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using LSSOFT.Models;
using System;
using System.Threading.Tasks;
using Avalonia.Threading;
using System.Threading;
using System.Collections.Generic;

namespace LSSOFT.ViewModels;

public partial class SoftwareLocalizationViewModel : ObservableObject
{
    [ObservableProperty]
    private ObservableCollection<SoftwarePackageInfo> _localPackages = new();

    [ObservableProperty]
    private string _scanPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + @"\Downloads";

    [ObservableProperty]
    private ObservableCollection<SoftwareCategoryInfo> _categories = new();

    [ObservableProperty]
    private bool _isAddCategoryDialogVisible;

    [ObservableProperty]
    private string _newCategoryName = string.Empty;

    private CancellationTokenSource? _scanCts;

    public SoftwareLocalizationViewModel()
    {
        InitializeCategories();
        // 延迟扫描，避免构造函数中的阻塞操作
        _ = Task.Run(async () =>
        {
            await Task.Delay(500); // 给UI时间完成初始化
            await ScanAndLoadPackagesAsync();
        });
    }

    private void InitializeCategories()
    {
        Categories.Clear();
        Categories.Add(new SoftwareCategoryInfo("全部"));
        Categories.Add(new SoftwareCategoryInfo("系统"));
        Categories.Add(new SoftwareCategoryInfo("办公"));
        Categories.Add(new SoftwareCategoryInfo("开发"));
        Categories.Add(new SoftwareCategoryInfo("设计"));
        Categories.Add(new SoftwareCategoryInfo("游戏"));
        Categories.Add(new SoftwareCategoryInfo("手机"));
        Categories.Add(new SoftwareCategoryInfo("其它"));
        if (Categories.Count > 0)
            Categories[0].IsSelected = true;
    }

    [RelayCommand]
    public async Task ScanAndLoadPackagesAsync()
    {
        try
        {
            // 取消之前的扫描
            _scanCts?.Cancel();
            var cts = new CancellationTokenSource();
            _scanCts = cts;
            var token = cts.Token;

            var scanPath = ScanPath;
            System.Diagnostics.Debug.WriteLine($"开始扫描路径: {scanPath}");

            // 检查路径是否存在
            if (string.IsNullOrEmpty(scanPath) || !Directory.Exists(scanPath))
            {
                System.Diagnostics.Debug.WriteLine($"路径不存在或为空: {scanPath}");
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => LocalPackages.Clear());
                return;
            }

            // 在后台线程执行文件扫描
            var fileList = await Task.Run(() =>
            {
                try
                {
                    var exts = new[] { ".exe", ".msi", ".zip", ".rar", ".7z", ".tar", ".gz" };
                    var files = Directory.GetFiles(scanPath, "*.*", SearchOption.TopDirectoryOnly)
                        .Where(f => exts.Contains(Path.GetExtension(f).ToLowerInvariant()))
                        .Take(1000) // 限制最大文件数量，避免过多文件导致卡顿
                        .Select(f => new FileInfo(f))
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"找到 {files.Count} 个安装包文件");
                    return files;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"扫描文件失败: {ex.Message}");
                    return new List<FileInfo>();
                }
            }, token);

            token.ThrowIfCancellationRequested();

            // 转换为显示对象
            var result = fileList.Select(fi => new SoftwarePackageInfo
            {
                Name = fi.Name,
                FilePath = fi.FullName,
                Size = FormatFileSize(fi.Length),
                CreatedDate = fi.CreationTime,
                Description = fi.FullName,
                Content = fi.FullName,
                License = "本地文件",
                Stars = "-",
                Downloads = "-",
                SupportDonation = false,
                SupportAI = false,
                Version = string.Empty
            }).ToList();

            // 在UI线程更新集合
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                LocalPackages.Clear();
                foreach (var pkg in result)
                {
                    LocalPackages.Add(pkg);
                }
                System.Diagnostics.Debug.WriteLine($"已加载 {LocalPackages.Count} 个安装包到界面");
            });
        }
        catch (OperationCanceledException)
        {
            System.Diagnostics.Debug.WriteLine("扫描操作被取消");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"扫描失败: {ex.Message}");
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => LocalPackages.Clear());
        }
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes >= 1024 * 1024 * 1024)
            return $"{bytes / 1024.0 / 1024.0 / 1024.0:F1} GB";
        else if (bytes >= 1024 * 1024)
            return $"{bytes / 1024.0 / 1024.0:F1} MB";
        else if (bytes >= 1024)
            return $"{bytes / 1024.0:F1} KB";
        else
            return $"{bytes} B";
    }

    partial void OnScanPathChanged(string value)
    {
        // 延迟扫描，避免路径变化时立即触发
        _ = Task.Run(async () =>
        {
            await Task.Delay(300); // 防抖动延迟
            await ScanAndLoadPackagesAsync();
        });
    }

    [RelayCommand]
    private void ShowAddCategoryDialog()
    {
        NewCategoryName = string.Empty;
        IsAddCategoryDialogVisible = true;
    }

    [RelayCommand]
    private void FilterByCategory(string categoryName)
    {
        foreach (var category in Categories)
        {
            category.IsSelected = category.Name == categoryName;
        }
        // 这里只做选中高亮，实际可扩展为筛选 LocalPackages
    }
}
