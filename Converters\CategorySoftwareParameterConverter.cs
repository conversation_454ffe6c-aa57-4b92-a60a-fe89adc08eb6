using Avalonia.Data.Converters;
using LSSOFT.Models;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace LSSOFT.Converters
{
    /// <summary>
    /// 将分类名称和软件信息组合成参数对象的转换器
    /// </summary>
    public class CategorySoftwareParameterConverter : IMultiValueConverter
    {
        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values.Count >= 2 && 
                values[0] is string categoryName && 
                values[1] is SoftwarePackageInfo software)
            {
                return new { CategoryName = categoryName, Software = software };
            }

            return null;
        }

        public object[] ConvertBack(object? value, Type[] targetTypes, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
