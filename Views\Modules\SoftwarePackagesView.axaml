<UserControl
    x:Class="LSSOFT.Views.Modules.SoftwarePackagesView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:vm="using:LSSOFT.ViewModels"
    x:DataType="vm:SoftwarePackagesViewModel">



    <Grid Margin="10" Grid.IsSharedSizeScope="True">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  标题区域  -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  左侧标题和描述  -->
            <StackPanel Grid.Column="0" VerticalAlignment="Center" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,10,0" VerticalAlignment="Center"
                    Text="📦" FontSize="24" />
                <TextBlock
                    VerticalAlignment="Center" Text="软件安装管理"
                    FontSize="24" FontWeight="Bold" />
                <!--  描述文字  -->
                <TextBlock
                    VerticalAlignment="Center"
                    Text=" ——软件安装包下载、分类、注册、破解等功能。" FontSize="16"
                    Foreground="#718096" />
            </StackPanel>

            <!--  右侧操作按钮区域  -->
            <StackPanel Grid.Column="2"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Orientation="Horizontal" Spacing="2">

                <TextBox
                    Name="SearchBox" Width="150"
                    VerticalAlignment="Center" Watermark="搜索软件..."
                    Text="{Binding SearchText}" />
                <Button
                    Width="35" Height="35"
                    ToolTip.Tip="卡片视图"
                    Command="{Binding SwitchToCardViewCommand}"
                    Classes="view-toggle-button"
                    Classes.selected="{Binding IsCardView}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" Text="🔲"
                        FontSize="16" />
                </Button>
                <Button
                    Width="35" Height="35"
                    ToolTip.Tip="列表视图"
                    Command="{Binding SwitchToListViewCommand}"
                    Classes="view-toggle-button"
                    Classes.selected="{Binding IsListView}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" Text="☰"
                        FontSize="16" />
                </Button>
                <Button
                    Width="35" Height="35"
                    VerticalAlignment="Center" ToolTip.Tip="更多操作"
                    Command="{Binding ShowMoreActionsCommand}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" Text="⋯"
                        FontSize="16" />
                </Button>
            </StackPanel>
        </Grid>

        <!--  分类标签  -->
        <Grid Grid.Row="2" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  分类按钮列表 - 支持多行显示  -->
            <ItemsControl Grid.Column="0" HorizontalAlignment="Left" ItemsSource="{Binding Categories}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Button
                            Margin="0,0,5,5"
                            Content="{Binding Name}"
                            Classes="category-button"
                            Command="{Binding $parent[UserControl].DataContext.FilterByCategoryCommand}"
                            CommandParameter="{Binding Name}"
                            Classes.selected="{Binding IsSelected}" />
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>

            <!--  添加分类按钮 - 固定在右侧  -->
            <Button Grid.Column="1"
                Margin="5,0,0,5" HorizontalAlignment="Right"
                VerticalAlignment="Top" Content="➕ 添加分类"
                ToolTip.Tip="添加自定义软件分类"
                Classes="add-category-button"
                Command="{Binding ShowAddCategoryDialogCommand}" />
        </Grid>

        <!--  软件包网格展示区域  -->
        <ScrollViewer Grid.Row="3"
            Name="SoftwareScrollViewer" Margin="0,0,0,20"
            VerticalScrollBarVisibility="Hidden"
            HorizontalScrollBarVisibility="Disabled"
            AllowAutoHide="True"
            IsScrollChainingEnabled="True">
            <StackPanel HorizontalAlignment="Stretch">
                <!--  列表视图表头  -->
                <Border
                    Padding="15,10" HorizontalAlignment="Stretch"
                    Background="#F8FAFC" BorderBrush="#E5E7EB"
                    BorderThickness="0,0,0,1"
                    IsVisible="{Binding IsListView}">
                    <Grid HorizontalAlignment="Stretch">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col1" />
                            <ColumnDefinition Width="300" SharedSizeGroup="col2" />
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col3" />
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col4" />
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col5" />
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col6" />
                            <ColumnDefinition Width="Auto" SharedSizeGroup="col7" />
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                            Width="30" Margin="10,0,10,0"
                            Text="图标" FontSize="12"
                            FontWeight="Bold" Foreground="#374151" />
                        <TextBlock Grid.Column="1"
                            Margin="0,0,15,0" Text="软件名称"
                            FontSize="12" FontWeight="Bold"
                            Foreground="#374151" />
                        <TextBlock Grid.Column="2"
                            Width="50" Margin="0,0,15,0"
                            Text="大小" FontSize="12"
                            FontWeight="Bold" Foreground="#374151"
                            TextAlignment="Center" />
                        <TextBlock Grid.Column="3"
                            Width="50" Margin="0,0,15,0"
                            Text="已装版本" FontSize="12"
                            FontWeight="Bold" Foreground="#374151"
                            TextAlignment="Center" />
                        <TextBlock Grid.Column="4"
                            Width="50" Margin="0,0,15,0"
                            Text="最新版本" FontSize="12"
                            FontWeight="Bold" Foreground="#374151"
                            TextAlignment="Center" />
                        <TextBlock Grid.Column="5"
                            Width="150" Margin="0,0,15,0"
                            Text="状态" FontSize="12"
                            FontWeight="Bold" Foreground="#374151"
                            TextAlignment="Center" />
                        <TextBlock Grid.Column="6"
                            Text="操作" FontSize="12"
                            FontWeight="Bold" Foreground="#374151"
                            TextAlignment="Center" />
                    </Grid>
                </Border>

                <!--  卡片模式 ItemsControl  -->
                <ItemsControl ItemsSource="{Binding FilteredSoftwarePackages}" IsVisible="{Binding IsCardView}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <!--  卡片视图  -->
                                <Border
                                    Width="300" Height="150"
                                    Margin="8" CornerRadius="8"
                                    Background="White" BorderBrush="#E5E7EB"
                                    BorderThickness="1">
                                    <Grid Margin="12,8,12,6">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <!--  第一行：图标和软件名称  -->
                                        <Grid Grid.Row="0" Margin="0,0,0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <!--  左侧图标  -->
                                            <Border Grid.Column="0"
                                                Width="56" Height="56"
                                                Margin="0,0,10,0" CornerRadius="6"
                                                Background="#F3F4F6" BorderBrush="#3B82F6"
                                                BorderThickness="1">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="📦"
                                                    FontSize="32" />
                                            </Border>

                                            <!--  右侧软件名称和描述  -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Top">
                                                <TextBlock
                                                    Margin="0,0,0,3"
                                                    Text="{Binding Name}"
                                                    FontSize="16" FontWeight="Bold"
                                                    Foreground="#1F2937" />
                                                <TextBlock
                                                    MaxHeight="30"
                                                    Text="{Binding Description}"
                                                    FontSize="11" Foreground="#6B7280"
                                                    TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Grid>

                                        <!--  第二行：软件版本信息  -->
                                        <StackPanel Grid.Row="1" Margin="0,0,0,4">
                                            <StackPanel VerticalAlignment="Center" Orientation="Horizontal" Spacing="5">
                                                <!--  大小信息  -->
                                                <TextBlock Text="大小:" FontSize="10" Foreground="#6B7280" />
                                                <TextBlock
                                                    Text="{Binding Size}"
                                                    FontSize="10" Foreground="#1F2937"
                                                    FontWeight="SemiBold" />
                                                <!--  已装版本  -->
                                                <TextBlock Text="已装版本:" FontSize="10" Foreground="#6B7280" />
                                                <TextBlock Text="{Binding Version}" FontSize="10" Foreground="#1F2937" />
                                                <!--  最新版本  -->
                                                <TextBlock Text="最新版本:" FontSize="10" Foreground="#6B7280" />
                                                <TextBlock Text="{Binding LatestVersion}" FontSize="10" Foreground="#10B981" />
                                            </StackPanel>
                                        </StackPanel>

                                        <!--  第三行：支持的插件和系统标签  -->
                                        <StackPanel Grid.Row="2" Margin="0,0,0,1">
                                            <!--  自动换行的标签容器  -->
                                            <WrapPanel Orientation="Horizontal">
                                                <!--  License许可证  -->
                                                <Border
                                                    Margin="0,0,2,2" Padding="4,2"
                                                    Background="#6B7280" CornerRadius="2">
                                                    <StackPanel Orientation="Horizontal" Spacing="2">
                                                        <TextBlock
                                                            Text="协议" FontSize="8"
                                                            Foreground="White" FontWeight="Bold" />
                                                        <TextBlock
                                                            Text="{Binding License}"
                                                            FontSize="8" Foreground="#60A5FA"
                                                            FontWeight="Bold" />
                                                    </StackPanel>
                                                </Border>

                                                <!--  Stars评分  -->
                                                <Border
                                                    Margin="0,0,2,2" Padding="4,2"
                                                    Background="#F97316" CornerRadius="2">
                                                    <StackPanel Orientation="Horizontal" Spacing="2">
                                                        <TextBlock
                                                            Text="收藏数量" FontSize="8"
                                                            Foreground="White" FontWeight="Bold" />
                                                        <TextBlock
                                                            Text="{Binding Stars}"
                                                            FontSize="8" Foreground="White"
                                                            FontWeight="Bold" />
                                                    </StackPanel>
                                                </Border>

                                                <!--  Downloads下载量  -->
                                                <Border
                                                    Margin="0,0,2,2" Padding="4,2"
                                                    Background="#22C55E" CornerRadius="2">
                                                    <StackPanel Orientation="Horizontal" Spacing="2">
                                                        <TextBlock
                                                            Text="下载量" FontSize="8"
                                                            Foreground="White" FontWeight="Bold" />
                                                        <TextBlock
                                                            Text="{Binding Downloads}"
                                                            FontSize="8" Foreground="White"
                                                            FontWeight="Bold" />
                                                    </StackPanel>
                                                </Border>
                                                <!--  Ask DeepWiki  -->
                                                <Border
                                                    Margin="0,0,2,2" Padding="4,2"
                                                    Background="#0EA5E9" CornerRadius="2"
                                                    IsVisible="{Binding SupportAI}">
                                                    <StackPanel Orientation="Horizontal" Spacing="2">
                                                        <TextBlock Text="🤖" FontSize="8" Foreground="White" />
                                                        <TextBlock
                                                            Text="Ask DeepWiki" FontSize="8"
                                                            Foreground="White" FontWeight="Bold" />
                                                    </StackPanel>
                                                </Border>
                                            </WrapPanel>
                                        </StackPanel>

                                        <!--  第四行：底部图标栏  -->
                                        <StackPanel Grid.Row="4"
                                            Margin="0,1,0,0" HorizontalAlignment="Left"
                                            VerticalAlignment="Bottom"
                                            Orientation="Horizontal" Spacing="10">
                                            <!--  主页  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe635;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="主页" />
                                            </Border>
                                            <!--  GitHub  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xea0a;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="开源" />
                                            </Border>
                                            <!--  收费  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe629;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="购买" />
                                            </Border>
                                            <!--  下载  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe694;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="下载" />
                                            </Border>
                                            <!--  视频  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe644;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="视频" />
                                            </Border>
                                            <!--  破解  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe6b1;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="破解" />
                                            </Border>
                                            <!--  收藏图标  -->
                                            <Border Padding="1" Background="Transparent">
                                                <TextBlock
                                                    Text="&#xe6b7;"
                                                    FontFamily="{StaticResource IconFont}"
                                                    FontSize="16" Foreground="#6B7280"
                                                    ToolTip.Tip="收藏" />
                                            </Border>

                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!--  列表模式 ItemsControl  -->
                <ItemsControl ItemsSource="{Binding FilteredSoftwarePackages}" IsVisible="{Binding IsListView}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel HorizontalAlignment="Stretch" Orientation="Vertical" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid HorizontalAlignment="Stretch">
                                <!--  列表视图  -->
                                <Border
                                    Padding="10,10" HorizontalAlignment="Stretch"
                                    Background="White" BorderBrush="#E5E7EB"
                                    BorderThickness="0,0,0,1">
                                    <Grid HorizontalAlignment="Stretch">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" SharedSizeGroup="col1" />
                                            <ColumnDefinition Width="300" SharedSizeGroup="col2" />
                                            <ColumnDefinition Width="Auto" SharedSizeGroup="col3" />
                                            <ColumnDefinition Width="Auto" SharedSizeGroup="col4" />
                                            <ColumnDefinition Width="Auto" SharedSizeGroup="col5" />
                                            <ColumnDefinition Width="Auto" SharedSizeGroup="col6" />
                                            <ColumnDefinition Width="*" SharedSizeGroup="col7" />
                                        </Grid.ColumnDefinitions>

                                        <!--  图标  -->
                                        <Border Grid.Column="0"
                                            Width="40" Height="40"
                                            Margin="0,0,0,0" VerticalAlignment="Center"
                                            CornerRadius="4" Background="#F3F4F6"
                                            BorderBrush="#3B82F6" BorderThickness="1">
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center" Text="📦"
                                                FontSize="25" />
                                        </Border>

                                        <!--  软件名称和描述  -->
                                        <StackPanel Grid.Column="1" Margin="0,0,10,0" VerticalAlignment="Center">
                                            <TextBlock
                                                Margin="0,0,0,2"
                                                Text="{Binding Name}"
                                                FontSize="14" FontWeight="Bold"
                                                Foreground="#1F2937" />
                                            <TextBlock
                                                Text="{Binding Description}"
                                                FontSize="11" Foreground="#6B7280"
                                                TextWrapping="NoWrap"
                                                TextTrimming="CharacterEllipsis" />
                                        </StackPanel>

                                        <!--  大小  -->
                                        <TextBlock Grid.Column="2"
                                            VerticalAlignment="Center"
                                            Text="{Binding Size}"
                                            FontSize="12" Foreground="#4B5563"
                                            TextAlignment="Center" />

                                        <!--  已装版本  -->
                                        <TextBlock Grid.Column="3"
                                            VerticalAlignment="Center"
                                            Text="{Binding Version}"
                                            FontSize="12" Foreground="#4B5563"
                                            TextAlignment="Center" />

                                        <!--  最新版本  -->
                                        <TextBlock Grid.Column="4"
                                            VerticalAlignment="Center"
                                            Text="{Binding LatestVersion}"
                                            FontSize="12" Foreground="#10B981"
                                            TextAlignment="Center" />

                                        <!--  状态标签  -->
                                        <StackPanel Grid.Column="5" VerticalAlignment="Center" Orientation="Horizontal">
                                            <Border
                                                Margin="0,0,3,0" Padding="4,2"
                                                VerticalAlignment="Center" Background="#22C55E"
                                                CornerRadius="2">
                                                <TextBlock
                                                    VerticalAlignment="Center" Text="已安装"
                                                    FontSize="9" Foreground="White"
                                                    FontWeight="Bold" />
                                            </Border>
                                            <!--  有更新  -->
                                            <Border
                                                Margin="0,0,3,0" Padding="4,2"
                                                VerticalAlignment="Center" Background="#F59E42"
                                                CornerRadius="2"
                                                IsVisible="{Binding HasUpdate}">
                                                <TextBlock
                                                    VerticalAlignment="Center" Text="有更新"
                                                    FontSize="9" Foreground="White"
                                                    FontWeight="Bold" />
                                            </Border>
                                            <!--  可破解  -->
                                            <Border
                                                Margin="0,0,3,0" Padding="4,2"
                                                VerticalAlignment="Center" Background="#EF4444"
                                                CornerRadius="2"
                                                IsVisible="{Binding CanCrack}">
                                                <TextBlock
                                                    VerticalAlignment="Center" Text="可破解"
                                                    FontSize="9" Foreground="White"
                                                    FontWeight="Bold" />
                                            </Border>
                                            <!--  已开源  -->
                                            <Border
                                                Margin="0,0,3,0" Padding="4,2"
                                                VerticalAlignment="Center" Background="#2563EB"
                                                CornerRadius="2"
                                                IsVisible="{Binding IsOpenSource}">
                                                <TextBlock
                                                    VerticalAlignment="Center" Text="已开源"
                                                    FontSize="9" Foreground="White"
                                                    FontWeight="Bold" />
                                            </Border>
                                        </StackPanel>

                                        <!--  操作按钮  -->
                                        <StackPanel Grid.Column="6"
                                            VerticalAlignment="Center"
                                            Orientation="Horizontal" Spacing="6">
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="下载" Classes="list-action-button">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="📥"
                                                    FontSize="20" />
                                            </Button>
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="更新" Classes="list-action-button">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="🔄"
                                                    FontSize="20" />
                                            </Button>
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="卸载" Classes="list-action-button">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="🗑️"
                                                    FontSize="20" />
                                            </Button>
                                            <!--  新增：忽略版本更新  -->
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="忽略版本更新" Classes="list-action-button"
                                                Command="{Binding $parent[UserControl].DataContext.IgnoreUpdateCommand}"
                                                CommandParameter="{Binding}">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="🚫"
                                                    FontSize="20" />
                                            </Button>
                                            <!--  新增：不再更新  -->
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="不再更新" Classes="list-action-button"
                                                Command="{Binding $parent[UserControl].DataContext.StopUpdateCommand}"
                                                CommandParameter="{Binding}">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="⏸️"
                                                    FontSize="20" />
                                            </Button>
                                            <!--  新增：打开安装目录  -->
                                            <Button
                                                Width="30" Height="30"
                                                Padding="0" VerticalAlignment="Center"
                                                ToolTip.Tip="打开安装目录" Classes="list-action-button"
                                                Command="{Binding $parent[UserControl].DataContext.OpenInstallDirCommand}"
                                                CommandParameter="{Binding}">
                                                <TextBlock
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center" Text="📂"
                                                    FontSize="20" />
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <!--  添加分类对话框  -->
        <Border Grid.RowSpan="4" Background="#80000000" IsVisible="{Binding IsAddCategoryDialogVisible}">
            <Border
                MaxWidth="400" Padding="20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" Background="White"
                CornerRadius="8" BorderBrush="#E2E8F0"
                BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock
                        HorizontalAlignment="Center" Text="添加软件分类"
                        FontSize="18" FontWeight="SemiBold" />

                    <TextBox Width="300" Watermark="请输入分类名称..." Text="{Binding NewCategoryName}" />

                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal" Spacing="10">
                        <Button
                            Width="80" Content="确定"
                            Command="{Binding AddCategoryCommand}"
                            Classes="primary-button" />
                        <Button
                            Width="80" Content="取消"
                            Command="{Binding CancelAddCategoryCommand}"
                            Classes="secondary-button" />
                    </StackPanel>
                </StackPanel>
            </Border>
        </Border>
    </Grid>

    <!--  样式定义  -->
    <UserControl.Styles>
        <Style Selector="Button.category-button">
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="Margin" Value="0,0,5,0" />
            <Setter Property="CornerRadius" Value="15" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  选中状态样式  -->
        <Style Selector="Button.category-button.selected">
            <Setter Property="Background" Value="#3182CE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#3182CE" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <Style Selector="Button.category-button.selected:pointerover">
            <Setter Property="Background" Value="#2C5AA0" />
            <Setter Property="BorderBrush" Value="#2C5AA0" />
        </Style>

        <!--  添加分类按钮样式  -->
        <Style Selector="Button.add-category-button">
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.add-category-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <Style Selector="Button.add-category-button:pressed">
            <Setter Property="Background" Value="#E2E8F0" />
        </Style>

        <!--  对话框按钮样式  -->
        <Style Selector="Button.primary-button">
            <Setter Property="Background" Value="#3182CE" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.primary-button:pointerover">
            <Setter Property="Background" Value="#2C5AA0" />
        </Style>

        <Style Selector="Button.secondary-button">
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#4A5568" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E2E8F0" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="FontWeight" Value="Medium" />
        </Style>

        <Style Selector="Button.secondary-button:pointerover">
            <Setter Property="Background" Value="#F7FAFC" />
            <Setter Property="BorderBrush" Value="#CBD5E0" />
        </Style>

        <!--  视图切换按钮样式  -->
        <Style Selector="Button.view-toggle-button">
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#6B7280" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Margin" Value="0" />
        </Style>

        <Style Selector="Button.view-toggle-button:pointerover">
            <Setter Property="Background" Value="#F3F4F6" />
            <Setter Property="Foreground" Value="#374151" />
        </Style>

        <Style Selector="Button.view-toggle-button.selected">
            <Setter Property="Background" Value="#3B82F6" />
            <Setter Property="Foreground" Value="White" />
        </Style>

        <Style Selector="Button.view-toggle-button.selected:pointerover">
            <Setter Property="Background" Value="#2563EB" />
        </Style>

        <!--  列表操作按钮样式  -->
        <Style Selector="Button.list-action-button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="#6B7280" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#E5E7EB" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="FontSize" Value="12" />
        </Style>

        <Style Selector="Button.list-action-button:pointerover">
            <Setter Property="Background" Value="#F9FAFB" />
            <Setter Property="BorderBrush" Value="#D1D5DB" />
            <Setter Property="Foreground" Value="#374151" />
        </Style>
    </UserControl.Styles>

</UserControl>
